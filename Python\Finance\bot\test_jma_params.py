import os
import sys
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)

import strategies
import pandas as pd
import numpy as np
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def test_parameterized_function():
    """Test the parameterized JMA function with different parameter sets."""
    
    # Get data
    df = strategies.get_data(as_namedtuple=False)
    
    # Test 1: Default parameters (should match original function)
    print("\n" + "="*60)
    print("TEST 1: Default Parameters")
    print("="*60)
    
    df_original = strategies.get_jma_bbands_signals(df.copy())
    df_parameterized = strategies.get_jma_bbands_signals_parameterized(df.copy())
    
    original_signals = df_original["TotalSignal"].values
    param_signals = df_parameterized["TotalSignal"].values
    
    signals_match = np.array_equal(original_signals, param_signals)
    
    print(f"Signals match original function: {signals_match}")
    print(f"Original signals count: {np.sum(original_signals != 0)}")
    print(f"Parameterized signals count: {np.sum(param_signals != 0)}")
    
    if not signals_match:
        diff_indices = np.where(original_signals != param_signals)[0]
        print(f"Number of differences: {len(diff_indices)}")
        print(f"First 5 differences at indices: {diff_indices[:5].tolist()}")
    
    # Test 2: Custom parameters
    print("\n" + "="*60)
    print("TEST 2: Custom Parameters")
    print("="*60)
    
    custom_params = {
        'jma_slow_length': 60,
        'jma_slow_phase': 10,
        'jma_fast_length': 25,
        'jma_fast_phase': 30,
        'bb_length': 20,
        'bb_std': 2.0,
        'backcandles': 10,
        'use_rsi_filter': True,
        'rsi_length': 14,
        'rsi_overbought': 70,
        'rsi_oversold': 30
    }
    
    df_custom = strategies.get_jma_bbands_signals_parameterized(df.copy(), **custom_params)
    custom_signals = df_custom["TotalSignal"].values
    
    print(f"Custom parameters: {custom_params}")
    print(f"Custom signals count: {np.sum(custom_signals != 0)}")
    print(f"Buy signals: {np.sum(custom_signals == 2)}")
    print(f"Sell signals: {np.sum(custom_signals == 1)}")
    
    # Test 3: RSI filter comparison
    print("\n" + "="*60)
    print("TEST 3: RSI Filter Comparison")
    print("="*60)
    
    df_no_rsi = strategies.get_jma_bbands_signals_parameterized(df.copy(), use_rsi_filter=False)
    df_with_rsi = strategies.get_jma_bbands_signals_parameterized(df.copy(), use_rsi_filter=True)
    
    no_rsi_signals = df_no_rsi["TotalSignal"].values
    with_rsi_signals = df_with_rsi["TotalSignal"].values
    
    print(f"Signals without RSI filter: {np.sum(no_rsi_signals != 0)}")
    print(f"Signals with RSI filter: {np.sum(with_rsi_signals != 0)}")
    print(f"Signals filtered out by RSI: {np.sum(no_rsi_signals != 0) - np.sum(with_rsi_signals != 0)}")
    
    # Test 4: Extreme parameters
    print("\n" + "="*60)
    print("TEST 4: Extreme Parameters")
    print("="*60)
    
    extreme_params = {
        'jma_slow_length': 100,
        'jma_fast_length': 10,
        'bb_length': 5,
        'bb_std': 3.0,
        'backcandles': 20
    }
    
    try:
        df_extreme = strategies.get_jma_bbands_signals_parameterized(df.copy(), **extreme_params)
        extreme_signals = df_extreme["TotalSignal"].values
        print(f"Extreme parameters test passed")
        print(f"Signals generated: {np.sum(extreme_signals != 0)}")
    except Exception as e:
        print(f"Extreme parameters test failed: {str(e)}")
    
    # Analyze signal correctness
    print("\n" + "="*60)
    print("SIGNAL ACCURACY ANALYSIS")
    print("="*60)
    
    def quick_accuracy_check(df_signals, label):
        signals = df_signals["TotalSignal"].values
        correct = 0
        total = 0
        
        for i in range(len(signals) - 1):
            if signals[i] == 2:  # Buy signal
                total += 1
                if i + 1 < len(df_signals) and df_signals.iloc[i + 1]['Close'] > df_signals.iloc[i + 1]['Open']:
                    correct += 1
            elif signals[i] == 1:  # Sell signal
                total += 1
                if i + 1 < len(df_signals) and df_signals.iloc[i + 1]['Close'] < df_signals.iloc[i + 1]['Open']:
                    correct += 1
        
        accuracy = correct / total if total > 0 else 0
        print(f"{label}: {correct}/{total} correct ({accuracy:.2%})")
    
    quick_accuracy_check(df_original, "Original function")
    quick_accuracy_check(df_custom, "Custom parameters")
    quick_accuracy_check(df_no_rsi, "No RSI filter")
    quick_accuracy_check(df_with_rsi, "With RSI filter")
    
    print("\n" + "="*60)
    print("All tests completed successfully!")
    print("="*60)


if __name__ == "__main__":
    test_parameterized_function() 