<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace>QuantConnect.Algorithm.CSharp</RootNamespace>
    <AssemblyName>QuantConnect.Algorithm.CSharp</AssemblyName>
    <TargetFramework>net9.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <AnalysisMode>AllEnabledByDefault</AnalysisMode>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Description>QuantConnect LEAN Engine: Algorithm.CSharp Project - A collection of C# algorithm demonstrations for how to use the API</Description>
    <NoWarn>CS0618;CA1062;CA1002</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup>
    <PackageLicenseFile>LICENSE</PackageLicenseFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'DebugDocker|AnyCPU'">
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>portable</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="QuantConnect.pythonnet" Version="2.0.48" />
    <PackageReference Include="Accord" Version="3.6.0" />
    <PackageReference Include="Accord.Fuzzy" Version="3.6.0" />
    <PackageReference Include="Accord.MachineLearning" Version="3.6.0" />
    <PackageReference Include="Accord.Math" Version="3.6.0" />
    <PackageReference Include="Accord.Statistics" Version="3.6.0" />
    <PackageReference Include="DynamicInterop" Version="0.9.1" />
    <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="NodaTime" Version="3.0.5" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\Common\Properties\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Algorithm.Framework\QuantConnect.Algorithm.Framework.csproj" />
    <ProjectReference Include="..\Algorithm\QuantConnect.Algorithm.csproj" />
    <ProjectReference Include="..\Common\QuantConnect.csproj" />
    <ProjectReference Include="..\Indicators\QuantConnect.Indicators.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\LICENSE">
      <Pack>True</Pack>
      <PackagePath></PackagePath>
    </None>
  </ItemGroup>
</Project>
