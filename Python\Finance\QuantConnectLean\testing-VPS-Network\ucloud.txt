ubuntu@10-40-68-243:~$ sudo ab -n 1000 -c 10 "https://api.binance.com/api/v3/ticker/price?symbol=BNBBTC"
This is ApacheBench, Version 2.3 <$Revision: 1903618 $>
Copyright 1996 Adam <PERSON>, Zeus Technology Ltd, http://www.zeustech.net/
Licensed to The Apache Software Foundation, http://www.apache.org/

Benchmarking api.binance.com (be patient)
Completed 100 requests
Completed 200 requests
Completed 300 requests
Completed 400 requests
Completed 500 requests
Completed 600 requests
Completed 700 requests
Completed 800 requests
Completed 900 requests
Completed 1000 requests
Finished 1000 requests


Server Software:        nginx
Server Hostname:        api.binance.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_128_GCM_SHA256,2048,128
Server Temp Key:        X25519 253 bits
TLS Server Name:        api.binance.com

Document Path:          /api/v3/ticker/price?symbol=BNBBTC
Document Length:        40 bytes

Concurrency Level:      10
Time taken for tests:   12.388 seconds
Complete requests:      1000
Failed requests:        0
Total transferred:      944646 bytes
HTML transferred:       40000 bytes
Requests per second:    80.72 [#/sec] (mean)
Time per request:       123.879 [ms] (mean)
Time per request:       12.388 [ms] (mean, across all concurrent requests)
Transfer rate:          74.47 [Kbytes/sec] received

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        3   95 282.3      6    2091
Processing:     4   25  57.8      8     418
Waiting:        4   25  57.8      8     418
Total:          9  121 289.1     14    2098

Percentage of the requests served within a certain time (ms)
  50%     14
  66%     16
  75%     18
  80%    215
  90%    223
  95%   1028
  98%   1252
  99%   1268
 100%   2098 (longest request)
