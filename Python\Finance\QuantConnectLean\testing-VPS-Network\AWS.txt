root@ip-172-31-35-230:/home/<USER>"https://api.binance.com/api/v3/ticker/price?symbol=BNBBTC"
This is ApacheBench, Version 2.3 <$Revision: 1903618 $>
Copyright 1996 Adam <PERSON>wi<PERSON>, Zeus Technology Ltd, http://www.zeustech.net/
Licensed to The Apache Software Foundation, http://www.apache.org/

Benchmarking api.binance.com (be patient).....done


Server Software:        nginx
Server Hostname:        api.binance.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_128_GCM_SHA256,2048,128
Server Temp Key:        X25519 253 bits
TLS Server Name:        api.binance.com

Document Path:          /api/v3/ticker/price?symbol=BNBBTC
Document Length:        40 bytes

Concurrency Level:      10
Time taken for tests:   0.149 seconds
Complete requests:      100
Failed requests:        0
Total transferred:      94194 bytes
HTML transferred:       4000 bytes
Requests per second:    671.32 [#/sec] (mean)
Time per request:       14.896 [ms] (mean)
Time per request:       1.490 [ms] (mean, across all concurrent requests)
Transfer rate:          617.52 [Kbytes/sec] received

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        3    6   2.0      6      13
Processing:     3    7   2.9      7      28
Waiting:        3    6   2.8      5      28
Total:          8   13   3.8     12      35

Percentage of the requests served within a certain time (ms)
  50%     12
  66%     15
  75%     15
  80%     16
  90%     17
  95%     20
  98%     22
  99%     35
 100%     35 (longest request)
root@ip-172-31-35-230:/home/<USER>