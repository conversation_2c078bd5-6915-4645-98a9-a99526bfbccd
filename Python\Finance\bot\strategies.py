# fmt: off
import os
import pickle
import sys
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
import utils
# fmt: on

import datetime as dt
import logging
import math
import strategies
import subprocess
import sys
import random
import zipfile
import io
from tqdm import tqdm

tqdm.pandas()  # This enables .progress_apply
import timeit
import threading
from pyinstrument import Profiler
import time
from concurrent.futures import thread
import pandas as pd
import pandas_ta as pta
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
import warnings
import numba
import vectorbtpro as vbt
from collections import namedtuple
import pandas as pd
import pendulum
from aligo import Aligo
import vbt_custom_simulation
from numba.types import boolean
from vectorbtpro.signals.factory import SignalFactory
import inspect
import warnings
warnings.filterwarnings('ignore', 'Series.__getitem__ treating keys as positions is deprecated')


def get_data(as_namedtuple=False):
    if 1:
        data_source = parentdir + r"/data/DOGEUSDT_Candlestick_(1m)_(2022-01-01-2025-06-30).zip"
        csv_filename = "DOGEUSDT_Candlestick_(1m)_(2022-01-01-2025-06-30).csv"
        
        with zipfile.ZipFile(data_source, 'r') as zip_file:
            with zip_file.open(csv_filename) as csv_file:
                csv_data = io.TextIOWrapper(csv_file, encoding='utf-8')
                df = pd.read_csv(csv_data, index_col="OpenTime", parse_dates=True)
                offset = 996669
                df = df.iloc[offset:offset+10000]
    elif 0:
        data_source = parentdir + r"/data/DOGEFDUSD_Candlestick_(1m)_(2025-03-04-2025-03-06).csv"
        df = pd.read_csv(data_source, index_col="OpenTime", parse_dates=True)
    elif 0:  # run QuantConnect data
        data_source = parentdir + r"/data/QuantConnectVilla-log_DOGFDUSD_Candlestick_5m_2024-06-24-2024-06-26.txt"

        data = []
        with open(data_source, "r") as file:
            for line in file:
                if "vbt:" in line:
                    # Extract the part after 'vbt: '
                    vbt_data = line.split("vbt: ")[1].strip()

                    # Remove the surrounding backticks (`) from the data
                    vbt_data = vbt_data.strip("`")

                    # Split the data by ', '
                    values = vbt_data.split(", ")

                    # Parse the values into appropriate types
                    open_time = pd.to_datetime(values[0])  # Convert to datetime
                    open_price = float(values[1])  # Convert to float
                    high = float(values[2])  # Convert to float
                    low = float(values[3])  # Convert to float
                    close = float(values[4])  # Convert to float
                    volume = float(values[5])  # Convert to float
                    signal = int(values[6])  # Convert to integer

                    # Append the parsed data as a row
                    data.append([open_time, open_price, high, low, close, volume, signal])

        # Create a pandas DataFrame
        columns = ["OpenTime", "Open", "High", "Low", "Close", "Volume", "Signal"]
        df = pd.DataFrame(data, columns=columns)
        df.set_index("OpenTime", inplace=True)
    elif 0:  # to test consistency with freqtrade
        xsymbol = "DOGEUSDT"
        data_source = xsymbol
        timeframe = int(os.environ["timeframe"])
        df = vbt.BinanceData.pull(xsymbol, start="2025-02-14", end="2025-02-21", timeframe=f"{timeframe}m").get()
        df.index = df.index.tz_convert(None)
        df = df.reset_index()
        columns_to_keep = ["Open time", "Open", "High", "Low", "Close", "Volume"]
        df = df[columns_to_keep]
        df.rename(columns={"Open time": "OpenTime"}, inplace=True)
        df.set_index("OpenTime", inplace=True)
    elif 0:
        xsymbol = "IQ"
        data_source = xsymbol
        os.environ["timeframe"] = str(24 * 60)
        df = vbt.YFData.pull(symbols=xsymbol, start="1949-10-01", end="2049-12-31", timeframe="1d").get()
        if len(df) < 100:
            os.environ["timeframe"] = "5"
            df = vbt.YFData.pull(symbols=xsymbol, start="1949-10-01", end="2049-12-31", timeframe=f"5m").get()

        df[["Open", "High", "Low", "Close"]] = df[["Open", "High", "Low", "Close"]].round(3)
        df.index = df.index.tz_convert(None)  # Remove timezone information
        columns_to_keep = ["Open", "High", "Low", "Close", "Volume"]
        df = df[columns_to_keep]
    else:
        data_source = parentdir + r"/data/2019-03-01_2019-09-01(BTC).csv"
        df = pd.read_csv(data_source)
        columns_to_keep = ["Open time", "Open", "High", "Low", "Close", "Volume"]
        df = df[columns_to_keep]
        df.rename(columns={"Open time": "OpenTime"}, inplace=True)
        df["OpenTime"] = df["OpenTime"].str.replace("+00:00", "", regex=False)
        df["OpenTime"] = pd.to_datetime(df["OpenTime"], format="%Y-%m-%d %H:%M:%S")
        df.set_index("OpenTime", inplace=True)

    # Remove rows where High == Low == Open == Close
    df = df[~((df["High"] == df["Low"]) & (df["Low"] == df["Open"]) & (df["Open"] == df["Close"]))]

    # Remove rows where Open == 0, or High == 0, or Low == 0, or Close == 0
    df = df[~((df["Open"] == 0) | (df["High"] == 0) | (df["Low"] == 0) | (df["Close"] == 0))]

    # Due to the potential for two consecutive trading days to include a weekend,
    # the selection of the two time intervals is based on their minimum values.
    # for example, the first 3 trading days of IQ encompassing a weekend:
    #   2018-03-29
    #   2018-04-02
    #   2018-04-03
    bar_interval = min(df.index[1] - df.index[0], df.index[2] - df.index[1])

    start_date = pendulum.instance(df.index.min())
    end_date = pendulum.instance(df.index.max())
    time_range_str = end_date.diff(start_date).in_words(locale="en")

    # fmt: off
    logging.info(f"\n\nData Source Info. timeframe: {os.environ["timeframe"]}m({data_source}):\n"
                 f"\t  TimeRange: `{time_range_str}`({df.index.min()}-{df.index.max()})\n"
                 f"\tBarInterval: `{bar_interval}`\n"
                 f"\t  TotalBars: `{len(df)}`\n"
                 )
    # fmt: on

    if not as_namedtuple:
        return df

    if 0:
        df.to_csv(r"data.csv")

    valid_signal_cols = "Signal" in df.columns
    if not valid_signal_cols:
        df["Signal"] = 0  # dummy column to make Numba signal functions work
    df_column_names = df.columns.tolist()
    MyContext = namedtuple("MyContext", ["OpenTime"] + df_column_names + ["freq_in_minutes", "has_signal_columns"])
    freq_in_minutes = int(os.environ["timeframe"])
    my_context_data = (df.index.values,) + tuple(df.to_numpy().T) + (freq_in_minutes, valid_signal_cols)
    my_context = MyContext._make(my_context_data)
    return my_context


# Python Backtest： Profitable Scalping Strategy with VWAP, Bollinger Bands and RSI Indicators
# https://www.youtube.com/watch?v=RbQaARxEW9o&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=30
def vwap_bbands(df):
    df["VWAP"] = pta.vwap(df.High, df.Low, df.Close, df.Volume, anchor="D")  # https://pandas.pydata.org/docs/user_guide/timeseries.html#timeseries-period-aliases
    my_bbands = pta.bbands(df.Close, length=14, std=2.0)
    df = df.join(my_bbands)

    backcandles = 15

    downtrend_mask = df[["Open", "Close"]].max(axis=1).lt(df["VWAP"]).rolling(backcandles + 1).sum().eq(backcandles + 1)
    uptrend_mask = df[["Open", "Close"]].min(axis=1).gt(df["VWAP"]).rolling(backcandles + 1).sum().eq(backcandles + 1)
    df["downtrend"] = downtrend_mask
    df["uptrend"] = uptrend_mask

    df["VWAPSignal"] = 0

    df.loc[downtrend_mask, "VWAPSignal"] = 1
    df.loc[uptrend_mask, "VWAPSignal"] = 2

    TotSignal = pd.Series(0, index=df.index)
    TotSignal[(df.VWAPSignal == 2) & (df.Close <= df["BBL_14_2.0"])] = 2
    TotSignal[(df.VWAPSignal == 1) & (df.Close >= df["BBU_14_2.0"])] = 1
    df["TotalSignal"] = TotSignal

    columns_to_retain = ["Open", "High", "Low", "Close", "TotalSignal"]
    df = df[columns_to_retain]

    return df


@numba.njit(nogil=True)
def vwap_bbands_nb(
    data_context,
    backcandles=15,
    window=14,
    std=2.0,
):
    # Extract arrays from namedtuple
    open = data_context.Open
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    volume = data_context.Volume

    # Calculate group lengths for VWAP (daily anchor)
    # Assuming 288 5-min bars per day (24h * 12)
    group_lens = np.full(len(close) // 288 + 1, 288)
    group_lens[-1] = len(close) % 288

    # Calculate VWAP
    vwap = vbt.ind_nb.vwap_nb(
        high.reshape(-1, 1),
        low.reshape(-1, 1),
        close.reshape(-1, 1),
        volume.reshape(-1, 1),
        group_lens,
    ).flatten()

    # Calculate Bollinger Bands
    upper, middle, lower = vbt.ind_nb.bbands_nb(
        close.reshape(-1, 1),
        window=np.array([window]),
        alpha=np.array([std]),
    )
    upper = upper.flatten()
    lower = lower.flatten()

    # Calculate trend masks
    downtrend = np.zeros(len(close), dtype=np.bool_)
    uptrend = np.zeros(len(close), dtype=np.bool_)

    for i in range(backcandles, len(close)):
        down_count = 0
        up_count = 0
        for j in range(i - backcandles, i + 1):
            max_price = max(open[j], close[j])
            min_price = min(open[j], close[j])
            if max_price < vwap[j]:
                down_count += 1
            if min_price > vwap[j]:
                up_count += 1
        downtrend[i] = down_count == backcandles + 1
        uptrend[i] = up_count == backcandles + 1

    # Calculate signals
    total_signal = np.zeros(len(close))
    vwap_signal = np.zeros(len(close))
    vwap_signal[downtrend] = 1
    vwap_signal[uptrend] = 2

    # Generate final signals
    total_signal[(vwap_signal == 2) & (close <= lower)] = 2
    total_signal[(vwap_signal == 1) & (close >= upper)] = 1

    return total_signal


# Trading with Python: Simple Scalping Strategy with JMA
# https://www.youtube.com/watch?v=C3bh6Y4LpGs&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=21
def get_ema_bbands_signals(df):
    df["EMA_slow"] = pta.ema(df.Close, length=42)
    df["EMA_fast"] = pta.ema(df.Close, length=24)
    df["RSI"] = pta.rsi(df.Close, length=10)
    my_bbands = pta.bbands(df.Close, length=13, std=1.1)
    df["ATR"] = pta.atr(df.High, df.Low, df.Close, length=7)
    df = df.join(my_bbands)

    def ema_signal(df, current_candle, backcandles):
        df_slice = df.reset_index().copy()
        # Get the range of candles to consider
        start = max(0, current_candle - backcandles)
        end = current_candle
        relevant_rows = df_slice.iloc[start:end]

        # Check if all EMA_fast values are below EMA_slow values
        if all(relevant_rows["EMA_fast"] < relevant_rows["EMA_slow"]):
            return 1
        elif all(relevant_rows["EMA_fast"] > relevant_rows["EMA_slow"]):
            return 2
        else:
            return 0

    df.reset_index(inplace=True)
    df["EMASignal"] = df.progress_apply(lambda row: ema_signal(df, row.name, 7), axis=1)  # if row.name >= 20 else 0

    def total_signal(df, current_candle, backcandles):
        if (
            ema_signal(df, current_candle, backcandles) == 2
            and df.Close[current_candle] <= df["BBL_13_1.1"][current_candle]
            and df.RSI[current_candle]<(100 - 61.8 - 6.18)
        ):
            return 2
        if (
            ema_signal(df, current_candle, backcandles) == 1
            and df.Close[current_candle] >= df["BBU_13_1.1"][current_candle]
            and df.RSI[current_candle]>(61.8 + 6.18)
        ):

            return 1
        return 0

    df["TotalSignal"] = df.progress_apply(lambda row: total_signal(df, row.name, 7), axis=1)

    return df

def get_jma_bbands_signals(df):
    df["JMA_slow"] = pta.jma(df.Close, length=42, phase=0)
    df["JMA_fast"] = pta.jma(df.Close, length=24, phase=39)
    df["RSI"] = pta.rsi(df.Close, length=6)
    my_bbands = pta.bbands(df.Close, length=13, std=1.1)
    df["ATR"] = pta.atr(df.High, df.Low, df.Close, length=7)
    
    df = df.join(my_bbands)

    def jma_signal(df, current_candle, backcandles):
        df_slice = df.reset_index().copy()
        # Get the range of candles to consider
        start = max(0, current_candle - backcandles)
        end = current_candle
        relevant_rows = df_slice.iloc[start:end]

        # Check if all JMA_fast values are below JMA_slow values
        if all(relevant_rows["JMA_fast"] < relevant_rows["JMA_slow"]):
            return 1
        elif all(relevant_rows["JMA_fast"] > relevant_rows["JMA_slow"]):
            return 2
        else:
            return 0

    df.reset_index(inplace=True)
    df["JMASignal"] = df.progress_apply(lambda row: jma_signal(df, row.name, 7), axis=1)

    def total_signal(df, current_candle, backcandles):
        if (
            jma_signal(df, current_candle, backcandles) == 2
            and df.Close[current_candle] <= df["BBL_13_1.1"][current_candle]
            and df.RSI[current_candle]<(100 - 61.8 - 6.18)
        ):
            return 2
        if (
            jma_signal(df, current_candle, backcandles) == 1
            and df.Close[current_candle] >= df["BBU_13_1.1"][current_candle]
            and df.RSI[current_candle]>(61.8 + 6.18)
        ):
            return 1
        return 0

    df["TotalSignal"] = df.progress_apply(lambda row: total_signal(df, row.name, 7), axis=1)

    return df


@numba.njit(nogil=True)
def get_ema_bbands_signals_nb(
    data_context,
    rsi_len = 6,
    ema_slow_window=42,
    ema_fast_window=24,
    bb_window=13,
    bb_std=1.1,
    backcandles=7,
):
    """
    Generate trading signals using EMA crossovers, Bollinger Bands, and RSI.

    Parameters:
        data_context: Named tuple containing OHLCV data
        rsi_len: RSI period (default: 10)
        ema_slow_window: Slow EMA period (default: 42)
        ema_fast_window: Fast EMA period (default: 24)
        bb_window: Bollinger Bands period (default: 13)
        bb_std: Number of standard deviations for Bollinger Bands (default: 1.1)
        backcandles: Number of candles to look back for EMA trend (default: 7)

    Returns:
        Array of signals where:
        0 = no signal
        1 = sell signal
        2 = buy signal
    """
    assert len(data_context.Close) > max(ema_slow_window, ema_fast_window, bb_window, backcandles)
    if data_context.has_signal_columns:
        return data_context.Signal

    # Extract arrays from namedtuple
    close = data_context.Close

    close_reshaped = close.reshape(-1, 1)

    # Calculate RSI
    rsi = vbt.ind_nb.rsi_1d_nb(
        close=data_context.Close,
        window=rsi_len,
        wtype=3, # Wilder
        minp=None,
        adjust=False,
    )
    
    # Calculate EMAs
    ema_slow = vbt.ind_nb.ma_nb(
        close_reshaped,
        window=np.array([ema_slow_window]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    ema_fast = vbt.ind_nb.ma_nb(
        close_reshaped,
        window=np.array([ema_fast_window]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    # Calculate Bollinger Bands
    upper, middle, lower = vbt.ind_nb.bbands_nb(
        close_reshaped,
        window=np.array([bb_window]),
        alpha=np.array([bb_std]),
    )
    upper = upper.flatten()
    middle = middle.flatten()
    lower = lower.flatten()

    if 0:
        df_log = pd.DataFrame({
            'EMA_slow': ema_slow,
            'EMA_fast': ema_fast,
            'BB_lower': lower,
            'BB_upper': upper
        })
        logging.info("\n" + df_log.to_string())

    # Generate EMA signals
    ema_signal = np.zeros(len(close))

    for i in range(backcandles, len(close)):
        up_trend = True
        down_trend = True

        # Check EMA trend over backcandles, excluding current candle
        for j in range(i - backcandles, i):
            if ema_fast[j] < ema_slow[j]:
                up_trend = False
            if ema_fast[j] > ema_slow[j]:
                down_trend = False

        if up_trend:
            ema_signal[i] = 2
        elif down_trend:
            ema_signal[i] = 1

    # Generate final signals with RSI filter
    total_signal = np.zeros(len(close))

    for i in range(1, len(close)):
        if np.isnan(ema_slow[i]) or np.isnan(ema_fast[i]) or np.isnan(upper[i]) or np.isnan(lower[i]) or np.isnan(rsi[i]):
            continue

        # Buy signal: EMA uptrend + close <= BBL + RSI < 32.02
        if ema_signal[i] == 2 and close[i] <= lower[i] and rsi[i] < (100 - 61.8 - 6.18):
            total_signal[i] = 2
        # Sell signal: EMA downtrend + close >= BBU + RSI > 67.98
        elif ema_signal[i] == 1 and close[i] >= upper[i] and rsi[i] > (61.8 + 6.18):
            total_signal[i] = 1

    return total_signal


# Automated RSI Scalping Strategy Tested In Python
# https://www.youtube.com/watch?v=MzEX4XumtEE&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=60
def get_rsi_ema_scalping_signals(df):
    df["EMA200"] = pta.ema(df.Close, length=200)
    df["RSI"] = pta.rsi(df.Close, length=6)
    df["ATR"] = df.ta.atr()

    emasignal = [0] * len(df)
    backcandles = 8

    for row in range(backcandles - 1, len(df)):
        upt = 1
        dnt = 1
        for i in range(row - backcandles, row + 1):
            if df.High.iloc[row] >= df.EMA200.iloc[row]:
                dnt = 0
            if df.Low.iloc[row] <= df.EMA200.iloc[row]:
                upt = 0
        if upt == 1 and dnt == 1:
            # print("!!!!! check trend loop !!!!")
            emasignal[row] = 3
        elif upt == 1:
            emasignal[row] = 2
        elif dnt == 1:
            emasignal[row] = 1

    df["EMAsignal"] = emasignal

    TotSignal = [0] * len(df)
    for row in range(0, len(df)):
        TotSignal[row] = 0
        if df.EMAsignal.iloc[row] == 1 and df.RSI.iloc[row] >= 80:
            TotSignal[row] = 1
        if df.EMAsignal.iloc[row] == 2 and df.RSI.iloc[row] <= 20:
            TotSignal[row] = 2

    df["TotalSignal"] = TotSignal

    return df


@numba.njit(nogil=True)
def get_rsi_ema_scalping_signals_nb(
    data_context,
    ema_window=200,
    rsi_window=3,
    backcandles=8,
):
    """
    Generate trading signals using RSI and EMA scalping strategy
    Returns tuple of (total_signal, ema200, rsi, ema_signal)
    """
    # Extract arrays from namedtuple
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    length = len(close)

    # Calculate EMA200 using pandas_ta formula
    alpha = 2.0 / (ema_window + 1)
    ema200 = np.full(length, np.nan)  # Initialize with NaN

    # Calculate SMA for initial value
    if length >= ema_window:
        ema200[ema_window - 1] = np.mean(close[:ema_window])
        # Calculate EMA values after the initial period
        for i in range(ema_window, length):
            ema200[i] = alpha * close[i] + (1 - alpha) * ema200[i - 1]

    # Calculate RSI using pandas_ta formula
    rsi = np.full(length, np.nan)  # Initialize with NaN
    delta = np.zeros(length)
    gain = np.zeros(length)
    loss = np.zeros(length)

    # Calculate price changes
    for i in range(1, length):
        delta[i] = close[i] - close[i - 1]
        if delta[i] > 0:
            gain[i] = delta[i]
            loss[i] = 0
        else:
            gain[i] = 0
            loss[i] = abs(delta[i])

    # Calculate initial SMA values for gains and losses
    avg_gain = np.zeros(length)
    avg_loss = np.zeros(length)

    # First RSI value
    first_avg_gain = np.sum(gain[1 : rsi_window + 1]) / rsi_window
    first_avg_loss = np.sum(loss[1 : rsi_window + 1]) / rsi_window

    avg_gain[rsi_window] = first_avg_gain
    avg_loss[rsi_window] = first_avg_loss

    # Calculate first RSI value
    if first_avg_loss == 0:
        rsi[rsi_window] = 100
    else:
        rs = first_avg_gain / first_avg_loss
        rsi[rsi_window] = 100 - (100 / (1 + rs))

    # Calculate subsequent RSI values using Wilder's smoothing
    for i in range(rsi_window + 1, length):
        avg_gain[i] = ((avg_gain[i - 1] * (rsi_window - 1)) + gain[i]) / rsi_window
        avg_loss[i] = ((avg_loss[i - 1] * (rsi_window - 1)) + loss[i]) / rsi_window

        if avg_loss[i] == 0:
            rsi[i] = 100
        else:
            rs = avg_gain[i] / avg_loss[i]
            rsi[i] = 100 - (100 / (1 + rs))

    # Initialize signals array
    total_signal = np.zeros(length)
    ema_signal = np.zeros(length)

    # Calculate EMA signals
    for row in range(backcandles - 1, length):
        if np.isnan(ema200[row]):
            ema_signal[row] = 3  # Match pandas behavior for NaN periods
            continue

        upt = 1
        dnt = 1
        # Check only current candle against its EMA
        if high[row] >= ema200[row]:
            dnt = 0
        if low[row] <= ema200[row]:
            upt = 0

        if upt == 1 and dnt == 1:
            ema_signal[row] = 3
        elif upt == 1:
            ema_signal[row] = 2
        elif dnt == 1:
            ema_signal[row] = 1

    # Generate final signals
    for row in range(0, length):
        if np.isnan(ema200[row]):
            continue

        if ema_signal[row] == 1 and rsi[row] >= 90:
            total_signal[row] = 1
        if ema_signal[row] == 2 and rsi[row] <= 10:
            total_signal[row] = 2

    return total_signal


# Scalping Strategy With CandleStick Pattern Backtest In Python
# https://www.youtube.com/watch?v=ZoCpme03sVI&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=61
def get_candlestick_scalping_signals(df):
    df["EMA50"] = pta.ema(df.Close, length=50)
    df["EMA100"] = pta.ema(df.Close, length=100)
    df["EMA150"] = pta.ema(df.Close, length=150)
    df["ATR"] = df.ta.atr()

    backrollingN = 10
    df["slopeEMA50"] = df["EMA50"].diff(periods=1)
    df["slopeEMA50"] = df["slopeEMA50"].rolling(window=backrollingN).mean()

    df["slopeEMA100"] = df["EMA100"].diff(periods=1)
    df["slopeEMA100"] = df["slopeEMA100"].rolling(window=backrollingN).mean()

    df["slopeEMA150"] = df["EMA150"].diff(periods=1)
    df["slopeEMA150"] = df["slopeEMA150"].rolling(window=backrollingN).mean()

    conditions = [((df["EMA50"] < df["EMA100"]) & (df["EMA100"] < df["EMA150"]) & (df["slopeEMA50"] < 0) & (df["slopeEMA100"] < 0) & (df["slopeEMA150"] < 0)), ((df["EMA50"] > df["EMA100"]) & (df["EMA100"] > df["EMA150"]) & (df["slopeEMA50"] > 0) & (df["slopeEMA100"] > 0) & (df["slopeEMA150"] > 0))]
    choices = [1, 2]
    df["EMAsignal"] = np.select(conditions, choices, default=0)

    TotSignal = [0] * len(df)
    wicklimit = 2e-5
    for row in range(0, len(df)):
        TotSignal[row] = 0
        if df.EMAsignal.iloc[row] == 1 and df.Open.iloc[row] > df.EMA50.iloc[row] and df.Close.iloc[row] < df.EMA50.iloc[row] and df.High.iloc[row] - df.Open.iloc[row] <= wicklimit:
            TotSignal[row] = 1
        if df.EMAsignal.iloc[row] == 2 and df.Open.iloc[row] < df.EMA50.iloc[row] and df.Close.iloc[row] > df.EMA50.iloc[row] and df.Open.iloc[row] - df.Low.iloc[row] <= wicklimit:
            TotSignal[row] = 2

    df["TotalSignal"] = TotSignal

    return df


@numba.njit(nogil=True)
def get_candlestick_scalping_signals_nb(
    data_context,
    ema_window=50,
    backrollingN=10,
    wicklimit=2e-5,
):
    """
    Generate trading signals using EMA crossover strategy with wick limits
    """
    # Extract arrays from namedtuple
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    open = data_context.Open
    length = len(close)

    # Calculate EMAs
    ema50 = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([50]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    ema100 = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([100]),
        wtype=np.array([2]),
    ).flatten()

    ema150 = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([150]),
        wtype=np.array([2]),
    ).flatten()

    # Calculate slopes
    slopeEMA50 = np.zeros(length)
    slopeEMA100 = np.zeros(length)
    slopeEMA150 = np.zeros(length)

    # Initialize first values as NaN equivalent
    slopeEMA50[0] = np.nan
    slopeEMA100[0] = np.nan
    slopeEMA150[0] = np.nan

    for i in range(1, length):
        if np.isnan(ema50[i]) or np.isnan(ema50[i - 1]):
            slopeEMA50[i] = np.nan
        else:
            slopeEMA50[i] = ema50[i] - ema50[i - 1]

        if np.isnan(ema100[i]) or np.isnan(ema100[i - 1]):
            slopeEMA100[i] = np.nan
        else:
            slopeEMA100[i] = ema100[i] - ema100[i - 1]

        if np.isnan(ema150[i]) or np.isnan(ema150[i - 1]):
            slopeEMA150[i] = np.nan
        else:
            slopeEMA150[i] = ema150[i] - ema150[i - 1]

    # Calculate rolling means of slopes
    rolling_slopeEMA50 = np.zeros(length)
    rolling_slopeEMA100 = np.zeros(length)
    rolling_slopeEMA150 = np.zeros(length)

    for i in range(length):
        if i < backrollingN - 1:
            rolling_slopeEMA50[i] = np.nan
            rolling_slopeEMA100[i] = np.nan
            rolling_slopeEMA150[i] = np.nan
            continue

        window_sum50 = 0.0
        window_sum100 = 0.0
        window_sum150 = 0.0
        valid_count = 0

        for j in range(i - backrollingN + 1, i + 1):
            if not np.isnan(slopeEMA50[j]):
                window_sum50 += slopeEMA50[j]
                window_sum100 += slopeEMA100[j]
                window_sum150 += slopeEMA150[j]
                valid_count += 1

        if valid_count > 0:
            rolling_slopeEMA50[i] = window_sum50 / valid_count
            rolling_slopeEMA100[i] = window_sum100 / valid_count
            rolling_slopeEMA150[i] = window_sum150 / valid_count
        else:
            rolling_slopeEMA50[i] = np.nan
            rolling_slopeEMA100[i] = np.nan
            rolling_slopeEMA150[i] = np.nan

    # Initialize signals array
    total_signal = np.zeros(length)

    # Calculate signals
    for i in range(length):
        # Skip if any required value is NaN
        if np.isnan(ema50[i]) or np.isnan(ema100[i]) or np.isnan(ema150[i]) or np.isnan(rolling_slopeEMA50[i]) or np.isnan(rolling_slopeEMA100[i]) or np.isnan(rolling_slopeEMA150[i]):
            continue

        if ema50[i] < ema100[i] and ema100[i] < ema150[i] and rolling_slopeEMA50[i] < 0 and rolling_slopeEMA100[i] < 0 and rolling_slopeEMA150[i] < 0:
            if open[i] > ema50[i] and close[i] < ema50[i] and high[i] - open[i] <= wicklimit:
                total_signal[i] = 1
        elif ema50[i] > ema100[i] and ema100[i] > ema150[i] and rolling_slopeEMA50[i] > 0 and rolling_slopeEMA100[i] > 0 and rolling_slopeEMA150[i] > 0:
            if open[i] < ema50[i] and close[i] > ema50[i] and open[i] - low[i] <= wicklimit:
                total_signal[i] = 2

    return total_signal


# Simple EMA Scalping Trading Strategy Backtest In Python (Part 1)
# https://www.youtube.com/watch?v=NoNucP_IVB0&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=63
def get_ema_scalping_signals(df):
    df["EMA50"] = pta.ema(df.Close, length=50)
    df["EMA100"] = pta.ema(df.Close, length=100)
    df["EMA150"] = pta.ema(df.Close, length=150)

    backrollingN = 10
    df["slopeEMA50"] = df["EMA50"].diff(periods=1)
    df["slopeEMA50"] = df["slopeEMA50"].rolling(window=backrollingN).mean()

    df["slopeEMA100"] = df["EMA100"].diff(periods=1)
    df["slopeEMA100"] = df["slopeEMA100"].rolling(window=backrollingN).mean()

    df["slopeEMA150"] = df["EMA150"].diff(periods=1)
    df["slopeEMA150"] = df["slopeEMA150"].rolling(window=backrollingN).mean()

    conditions = [((df["EMA50"] < df["EMA100"]) & (df["EMA100"] < df["EMA150"]) & (df["slopeEMA50"] < 0) & (df["slopeEMA100"] < 0) & (df["slopeEMA150"] < 0)), ((df["EMA50"] > df["EMA100"]) & (df["EMA100"] > df["EMA150"]) & (df["slopeEMA50"] > 0) & (df["slopeEMA100"] > 0) & (df["slopeEMA150"] > 0))]
    choices = [1, 2]
    df["EMAsignal"] = np.select(conditions, choices, default=0)

    TotSignal = [0] * len(df)
    for row in range(0, len(df)):
        TotSignal[row] = 0
        if df.EMAsignal.iloc[row] == 1 and df.Open.iloc[row] > df.EMA50.iloc[row] and df.Close.iloc[row] < df.EMA50.iloc[row]:
            TotSignal[row] = 1
        if df.EMAsignal.iloc[row] == 2 and df.Open.iloc[row] < df.EMA50.iloc[row] and df.Close.iloc[row] > df.EMA50.iloc[row]:
            TotSignal[row] = 2

    df["TotalSignal"] = TotSignal

    return df


@numba.njit(nogil=True)
def get_ema_scalping_signals_nb(
    data_context,
    ema_window=50,
    backrollingN=10,
):
    """
    Generate trading signals using EMA scalping strategy with multiple EMAs
    """
    # Extract arrays from namedtuple
    open = data_context.Open
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    length = len(close)

    # Calculate EMAs
    ema50 = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([50]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    ema100 = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([100]),
        wtype=np.array([2]),
    ).flatten()

    ema150 = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([150]),
        wtype=np.array([2]),
    ).flatten()

    # Calculate slopes
    slopeEMA50 = np.zeros(length)
    slopeEMA100 = np.zeros(length)
    slopeEMA150 = np.zeros(length)

    # Calculate slopes (first differences)
    for i in range(1, length):
        if np.isnan(ema50[i]) or np.isnan(ema50[i - 1]):
            slopeEMA50[i] = np.nan
        else:
            slopeEMA50[i] = ema50[i] - ema50[i - 1]

        if np.isnan(ema100[i]) or np.isnan(ema100[i - 1]):
            slopeEMA100[i] = np.nan
        else:
            slopeEMA100[i] = ema100[i] - ema100[i - 1]

        if np.isnan(ema150[i]) or np.isnan(ema150[i - 1]):
            slopeEMA150[i] = np.nan
        else:
            slopeEMA150[i] = ema150[i] - ema150[i - 1]

    # Calculate rolling means of slopes
    rolling_slopeEMA50 = np.zeros(length)
    rolling_slopeEMA100 = np.zeros(length)
    rolling_slopeEMA150 = np.zeros(length)

    for i in range(length):
        if i < backrollingN - 1:
            rolling_slopeEMA50[i] = np.nan
            rolling_slopeEMA100[i] = np.nan
            rolling_slopeEMA150[i] = np.nan
            continue

        window_sum50 = 0.0
        window_sum100 = 0.0
        window_sum150 = 0.0
        valid_count = 0

        for j in range(i - backrollingN + 1, i + 1):
            if not np.isnan(slopeEMA50[j]):
                window_sum50 += slopeEMA50[j]
                window_sum100 += slopeEMA100[j]
                window_sum150 += slopeEMA150[j]
                valid_count += 1

        if valid_count > 0:
            rolling_slopeEMA50[i] = window_sum50 / valid_count
            rolling_slopeEMA100[i] = window_sum100 / valid_count
            rolling_slopeEMA150[i] = window_sum150 / valid_count
        else:
            rolling_slopeEMA50[i] = np.nan
            rolling_slopeEMA100[i] = np.nan
            rolling_slopeEMA150[i] = np.nan

    # Initialize signals array
    total_signal = np.zeros(length)

    # Generate final signals
    for i in range(length):
        if np.isnan(ema50[i]) or np.isnan(ema100[i]) or np.isnan(ema150[i]) or np.isnan(rolling_slopeEMA50[i]) or np.isnan(rolling_slopeEMA100[i]) or np.isnan(rolling_slopeEMA150[i]):
            continue

        # Check downtrend conditions
        if ema50[i] < ema100[i] and ema100[i] < ema150[i] and rolling_slopeEMA50[i] < 0 and rolling_slopeEMA100[i] < 0 and rolling_slopeEMA150[i] < 0:
            if open[i] > ema50[i] and close[i] < ema50[i]:
                total_signal[i] = 1

        # Check uptrend conditions
        elif ema50[i] > ema100[i] and ema100[i] > ema150[i] and rolling_slopeEMA50[i] > 0 and rolling_slopeEMA100[i] > 0 and rolling_slopeEMA150[i] > 0:
            if open[i] < ema50[i] and close[i] > ema50[i]:
                total_signal[i] = 2

    return total_signal


# Easy Crypto Trading: A Comprehensive Guide to VWAP Strategy in Python
# https://www.youtube.com/watch?v=0XfcnDAVY7Y&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=50
def get_vwap_scalping_signals(df):
    df["VWAP"] = pta.vwap(df.High, df.Low, df.Close, df.Volume)
    df["EMA"] = pta.ema(df.Close, length=100)

    emasignal = [0] * len(df)
    backcandles = 6

    for row in range(backcandles, len(df)):
        upt = 1
        dnt = 1
        for i in range(row - backcandles, row + 1):
            if df.High.iloc[i] >= df.EMA.iloc[i]:
                dnt = 0
            if df.Low.iloc[i] <= df.EMA.iloc[i]:
                upt = 0
        if upt == 1 and dnt == 1:
            # print("!!!!! check trend loop !!!!")
            emasignal[row] = 3
        elif upt == 1:
            emasignal[row] = 2
        elif dnt == 1:
            emasignal[row] = 1

    df["EMASignal"] = emasignal

    VWAPsignal = [0] * len(df)
    backcandles = 3

    for row in range(backcandles, len(df)):
        upt = 1
        dnt = 1
        for i in range(row - backcandles, row + 1):
            if df.High.iloc[i] >= df.VWAP.iloc[i]:
                dnt = 0
            if df.Low.iloc[i] <= df.VWAP.iloc[i]:
                upt = 0
        if upt == 1 and dnt == 1:
            # print("!!!!! check trend loop !!!!")
            VWAPsignal[row] = 3
        elif upt == 1:
            VWAPsignal[row] = 2
        elif dnt == 1:
            VWAPsignal[row] = 1

    df["VWAPSignal"] = VWAPsignal

    def TotalSignal(l):
        myclosedistance = 100
        if df.EMASignal.iloc[l] == 2 and df.VWAPSignal.iloc[l] == 2 and min(abs(df.VWAP.iloc[l] - df.High.iloc[l]), abs(df.VWAP.iloc[l] - df.Low.iloc[l])) <= myclosedistance:  # and df.EngulfingSignal[l]==2
            return 2
        if df.EMASignal.iloc[l] == 1 and df.VWAPSignal.iloc[l] == 1 and min(abs(df.VWAP.iloc[l] - df.High.iloc[l]), abs(df.VWAP.iloc[l] - df.Low.iloc[l])) <= myclosedistance:  # and df.EngulfingSignal[l]==1
            return 1
        else:
            return 0

    TotSignal = [0] * len(df)
    for row in range(0, len(df)):  # careful backcandles used previous cell
        TotSignal[row] = TotalSignal(row)
    df["TotalSignal"] = TotSignal
    return df


@numba.njit(nogil=True)
def get_vwap_scalping_signals_nb(
    data_context,
    ema_window=100,
    ema_backcandles=6,
    vwap_backcandles=3,
):
    """
    known issues with ema

    Generate trading signals using VWAP and EMA scalping strategy
    """
    # Extract arrays from namedtuple
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    volume = data_context.Volume
    length = len(close)

    # Calculate EMA using vectorbt's ma_nb
    ema = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([ema_window]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    # Calculate VWAP
    # Assuming 288 5-min bars per day (24h * 12)
    group_lens = np.full(length // 288 + 1, 288)
    group_lens[-1] = length % 288
    vwap = vbt.ind_nb.vwap_1d_nb(high, low, close, volume, group_lens)

    # Initialize signal arrays
    ema_signal = np.zeros(length)
    vwap_signal = np.zeros(length)
    total_signal = np.zeros(length)

    # Calculate EMA signals
    for row in range(ema_backcandles, length):
        upt = 1
        dnt = 1
        for i in range(row - ema_backcandles, row + 1):
            if high[i] >= ema[i]:
                dnt = 0
            if low[i] <= ema[i]:
                upt = 0
        if upt == 1 and dnt == 1:
            ema_signal[row] = 3
        elif upt == 1:
            ema_signal[row] = 2
        elif dnt == 1:
            ema_signal[row] = 1

    # Calculate VWAP signals
    for row in range(vwap_backcandles, length):
        upt = 1
        dnt = 1
        for i in range(row - vwap_backcandles, row + 1):
            if high[i] >= vwap[i]:
                dnt = 0
            if low[i] <= vwap[i]:
                upt = 0
        if upt == 1 and dnt == 1:
            vwap_signal[row] = 3
        elif upt == 1:
            vwap_signal[row] = 2
        elif dnt == 1:
            vwap_signal[row] = 1

    # Calculate final signals
    myclosedistance = 100
    for row in range(length):
        if np.isnan(vwap[row]) or np.isnan(ema[row]):
            continue

        min_distance = min(abs(vwap[row] - high[row]), abs(vwap[row] - low[row]))

        if ema_signal[row] == 2 and vwap_signal[row] == 2 and min_distance <= myclosedistance:
            total_signal[row] = 2
        elif ema_signal[row] == 1 and vwap_signal[row] == 1 and min_distance <= myclosedistance:
            total_signal[row] = 1

    return total_signal


# Price Action Strategy For Algorithmic Trading In Python
# https://www.youtube.com/watch?v=PfmxNxS4EYQ&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=53
def get_price_action_scalping_signals(df):
    df["EMA"] = pta.ema(df.Close, length=20)
    df["ATR"] = pta.atr(df.High, df.Low, df.Close, length=14)

    backrollingN = 20
    df["slopeEMA"] = df["EMA"].diff(periods=1)
    df["slopeEMA"] = df["slopeEMA"].rolling(window=backrollingN).mean()

    TotSignal = [0] * len(df)
    slopelimit = 5e-5
    percentlimit = 0.45
    Totsignalbackcandles_l = 10
    Totsignalbackcandles_r = 5
    for row in range(Totsignalbackcandles_l, len(df)):
        if (
            df.slopeEMA.iloc[row - Totsignalbackcandles_r] < -slopelimit
            and (min(df.Open.iloc[row - Totsignalbackcandles_r], df.Close.iloc[row - Totsignalbackcandles_r]) - df.Low.iloc[row - Totsignalbackcandles_r]) / (df.High.iloc[row - Totsignalbackcandles_r] - df.Low.iloc[row - Totsignalbackcandles_r]) > percentlimit
            and df.Low.iloc[row - Totsignalbackcandles_r] <= df.Low[row - Totsignalbackcandles_l : row].min()
        ):
            TotSignal[row - Totsignalbackcandles_r] = 1
        if (
            df.slopeEMA[row - Totsignalbackcandles_r] > slopelimit
            and (df.High.iloc[row - Totsignalbackcandles_r] - max(df.Open.iloc[row - Totsignalbackcandles_r], df.Close.iloc[row - Totsignalbackcandles_r])) / (df.High.iloc[row - Totsignalbackcandles_r] - df.Low.iloc[row - Totsignalbackcandles_r]) > percentlimit
            and df.High.iloc[row - Totsignalbackcandles_r] >= df.High.iloc[row - Totsignalbackcandles_l : row].max()
        ):
            TotSignal[row - Totsignalbackcandles_r] = 2

    df["TotalSignal"] = TotSignal
    return df


@numba.njit(nogil=True)
def get_price_action_scalping_signals_nb(
    data_context,
    ema_window=20,
    backrollingN=20,
    slopelimit=5e-5,
    percentlimit=0.45,
    Totsignalbackcandles_l=10,
    Totsignalbackcandles_r=5,
):
    """
    Generate trading signals using price action strategy with EMA and slope analysis
    """
    # Extract arrays from namedtuple
    open = data_context.Open
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    length = len(close)

    # Calculate EMA using vectorbt's ma_nb
    ema = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([ema_window]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    # Calculate slope EMA
    slopeEMA = np.zeros(length)

    # Calculate first differences for slope
    for i in range(1, length):
        if np.isnan(ema[i]) or np.isnan(ema[i - 1]):
            slopeEMA[i] = np.nan
        else:
            slopeEMA[i] = ema[i] - ema[i - 1]

    # Calculate rolling mean of slope
    rolling_slopeEMA = np.zeros(length)

    for i in range(length):
        if i < backrollingN - 1:
            rolling_slopeEMA[i] = np.nan
            continue

        window_sum = 0.0
        valid_count = 0

        for j in range(i - backrollingN + 1, i + 1):
            if not np.isnan(slopeEMA[j]):
                window_sum += slopeEMA[j]
                valid_count += 1

        if valid_count > 0:
            rolling_slopeEMA[i] = window_sum / valid_count
        else:
            rolling_slopeEMA[i] = np.nan

    # Initialize signals array
    total_signal = np.zeros(length)

    # Calculate signals
    for row in range(Totsignalbackcandles_l, length):
        if np.isnan(rolling_slopeEMA[row - Totsignalbackcandles_r]):
            continue

        # Calculate price ranges for signal conditions
        price_range = high[row - Totsignalbackcandles_r] - low[row - Totsignalbackcandles_r]
        if price_range == 0:  # Avoid division by zero
            continue

        # Check for sell signal
        if rolling_slopeEMA[row - Totsignalbackcandles_r] < -slopelimit:
            min_price = min(open[row - Totsignalbackcandles_r], close[row - Totsignalbackcandles_r])
            lower_wick_ratio = (min_price - low[row - Totsignalbackcandles_r]) / price_range

            # Find minimum low in lookback period
            min_low = low[row - Totsignalbackcandles_r]
            for i in range(row - Totsignalbackcandles_l, row):
                if low[i] < min_low:
                    min_low = low[i]

            if lower_wick_ratio > percentlimit and low[row - Totsignalbackcandles_r] <= min_low:
                total_signal[row - Totsignalbackcandles_r] = 1

        # Check for buy signal
        elif rolling_slopeEMA[row - Totsignalbackcandles_r] > slopelimit:
            max_price = max(open[row - Totsignalbackcandles_r], close[row - Totsignalbackcandles_r])
            upper_wick_ratio = (high[row - Totsignalbackcandles_r] - max_price) / price_range

            # Find maximum high in lookback period
            max_high = high[row - Totsignalbackcandles_r]
            for i in range(row - Totsignalbackcandles_l, row):
                if high[i] > max_high:
                    max_high = high[i]

            if upper_wick_ratio > percentlimit and high[row - Totsignalbackcandles_r] >= max_high:
                total_signal[row - Totsignalbackcandles_r] = 2

    return total_signal


# Python AlgoTrading Backtest: Using RSI and ADX with Moving Average for Buy/Sell Signals
# https://www.youtube.com/watch?v=NOBV08Im56Y&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=58
def get_adx_scalping_signals(df):
    df["EMA"] = pta.ema(df.Close, length=50)
    df["RSI"] = pta.rsi(df.Close, length=3)
    a = pta.adx(df.High, df.Low, df.Close, length=5)
    df["ADX"] = a["ADX_5"]
    df["ATR"] = df.ta.atr()

    emasignal = [0] * len(df)
    backcandles = 8

    for row in range(backcandles, len(df)):
        upt = 1
        dnt = 1
        for i in range(row - backcandles, row + 1):
            if df.High.iloc[i] >= df.EMA.iloc[i]:
                dnt = 0
            if df.Low.iloc[i] <= df.EMA.iloc[i]:
                upt = 0
        if upt == 1 and dnt == 1:
            # print("!!!!! check trend loop !!!!")
            emasignal[row] = 3
        elif upt == 1:
            emasignal[row] = 2
        elif dnt == 1:
            emasignal[row] = 1

    df["EMAsignal"] = emasignal

    RSIADXSignal = [0] * len(df)
    for row in range(0, len(df)):
        RSIADXSignal[row] = 0
        if df.EMAsignal.iloc[row] == 1 and df.RSI.iloc[row] >= 80 and df.ADX.iloc[row] >= 30:
            RSIADXSignal[row] = 1
        if df.EMAsignal.iloc[row] == 2 and df.RSI.iloc[row] <= 20 and df.ADX.iloc[row] >= 30:
            RSIADXSignal[row] = 2

    df["RSIADXSignal"] = RSIADXSignal

    CandleSignal = [0] * len(df)
    for row in range(1, len(df)):
        CandleSignal[row] = 0
        # if (RSIADXSignal[row]==1 or RSIADXSignal[row-1]==1) and (df.Open[row]>df.Close[row]):# and df.Close[row]<df.Close[row-1]):
        #    CandleSignal[row]=1
        # if (RSIADXSignal[row]==2 or RSIADXSignal[row-1]==2) and (df.Open[row]<df.Close[row]):# and df.Close[row]>df.Close[row-1]):
        #    CandleSignal[row]=2
        if RSIADXSignal[row - 1] == 1 and df.Open.iloc[row] > df.Close.iloc[row] and df.Close.iloc[row] < min(df.Close.iloc[row - 1], df.Open.iloc[row - 1]):
            CandleSignal[row] = 1
        if RSIADXSignal[row - 1] == 2 and df.Open.iloc[row] < df.Close.iloc[row] and df.Close.iloc[row] > max(df.Close.iloc[row - 1], df.Open.iloc[row - 1]):
            CandleSignal[row] = 2

    df["TotalSignal"] = CandleSignal
    return df


@numba.njit(nogil=True)
def get_adx_scalping_signals_nb(
    data_context,
    ema_window=50,
    rsi_window=3,
    adx_window=5,
    backcandles=8,
):
    """
    Generate trading signals using ADX, RSI and EMA scalping strategy
    """
    # Extract arrays from namedtuple
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    length = len(close)

    # Calculate EMA using vectorbt's ma_nb
    ema = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([ema_window]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    # Calculate RSI
    rsi = np.full(length, np.nan)
    delta = np.zeros(length)
    gain = np.zeros(length)
    loss = np.zeros(length)

    # Calculate price changes
    for i in range(1, length):
        delta[i] = close[i] - close[i - 1]
        if delta[i] > 0:
            gain[i] = delta[i]
            loss[i] = 0
        else:
            gain[i] = 0
            loss[i] = abs(delta[i])

    # Calculate initial SMA values for gains and losses
    avg_gain = np.zeros(length)
    avg_loss = np.zeros(length)

    # First RSI value
    first_avg_gain = np.sum(gain[1 : rsi_window + 1]) / rsi_window
    first_avg_loss = np.sum(loss[1 : rsi_window + 1]) / rsi_window

    avg_gain[rsi_window] = first_avg_gain
    avg_loss[rsi_window] = first_avg_loss

    # Calculate subsequent RSI values using Wilder's smoothing
    for i in range(rsi_window + 1, length):
        avg_gain[i] = ((avg_gain[i - 1] * (rsi_window - 1)) + gain[i]) / rsi_window
        avg_loss[i] = ((avg_loss[i - 1] * (rsi_window - 1)) + loss[i]) / rsi_window

        if avg_loss[i] == 0:
            rsi[i] = 100
        else:
            rs = avg_gain[i] / avg_loss[i]
            rsi[i] = 100 - (100 / (1 + rs))

    # Calculate ADX
    plus_di, minus_di, dx, adx = vbt.ind_nb.adx_1d_nb(
        high,
        low,
        close,
        window=adx_window,
        wtype=1,  # WType.Wilder = 1
    )

    # Initialize signal arrays
    ema_signal = np.zeros(length)
    total_signal = np.zeros(length)

    # Calculate EMA signals
    for row in range(backcandles, length):
        upt = 1
        dnt = 1
        for i in range(row - backcandles, row + 1):
            if high[i] >= ema[i]:
                dnt = 0
            if low[i] <= ema[i]:
                upt = 0
        if upt == 1 and dnt == 1:
            ema_signal[row] = 3
        elif upt == 1:
            ema_signal[row] = 2
        elif dnt == 1:
            ema_signal[row] = 1

    # Generate final signals
    for row in range(0, length):
        if np.isnan(ema[row]) or np.isnan(rsi[row]) or np.isnan(adx[row]):
            continue

        if ema_signal[row] == 1 and rsi[row] >= 80 and adx[row] >= 30:
            total_signal[row] = 1
        if ema_signal[row] == 2 and rsi[row] <= 20 and adx[row] >= 30:
            total_signal[row] = 2

    return total_signal


# Retracement Bar Coded In Python For Algorithmic Trading
# https://www.youtube.com/watch?v=NOBV08Im56Y&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=58
def get_retracement_signals(df):
    df["EMA"] = pta.ema(df.Close, length=20)
    backrollingN = 20
    df["slopeEMA"] = df["EMA"].diff(periods=1)
    df["slopeEMA"] = df["slopeEMA"].rolling(window=backrollingN).mean()

    TotSignal = [0] * len(df)
    slopelimit = 5e-5
    percentlimit = 0.45
    for row in range(0, len(df)):
        if df.slopeEMA.iloc[row] < -slopelimit and (min(df.Open.iloc[row], df.Close.iloc[row]) - df.Low.iloc[row]) / (df.High.iloc[row] - df.Low.iloc[row]) > percentlimit:
            TotSignal[row] = 1
        if df.slopeEMA.iloc[row] > slopelimit and (df.High.iloc[row] - max(df.Open.iloc[row], df.Close.iloc[row])) / (df.High.iloc[row] - df.Low.iloc[row]) > percentlimit:
            TotSignal[row] = 2

    df["TotalSignal"] = TotSignal
    return df


@numba.njit(nogil=True)
def get_retracement_signals_nb(
    data_context,
    ema_window=20,
    backrollingN=20,
    slopelimit=5e-5,
    percentlimit=0.45,
):
    """
    Generate trading signals using retracement strategy with EMA and slope analysis
    """
    # Extract arrays from namedtuple
    open = data_context.Open
    high = data_context.High
    low = data_context.Low
    close = data_context.Close
    length = len(close)

    # Calculate EMA using vectorbt's ma_nb
    ema = vbt.ind_nb.ma_nb(
        close.reshape(-1, 1),
        window=np.array([ema_window]),
        wtype=np.array([2]),  # WType.Exp = 2
    ).flatten()

    # Calculate slope EMA
    slopeEMA = np.zeros(length)

    # Calculate first differences for slope
    for i in range(1, length):
        if np.isnan(ema[i]) or np.isnan(ema[i - 1]):
            slopeEMA[i] = np.nan
        else:
            slopeEMA[i] = ema[i] - ema[i - 1]

    # Calculate rolling mean of slope
    rolling_slopeEMA = np.zeros(length)

    for i in range(length):
        if i < backrollingN - 1:
            rolling_slopeEMA[i] = np.nan
            continue

        window_sum = 0.0
        valid_count = 0

        for j in range(i - backrollingN + 1, i + 1):
            if not np.isnan(slopeEMA[j]):
                window_sum += slopeEMA[j]
                valid_count += 1

        if valid_count > 0:
            rolling_slopeEMA[i] = window_sum / valid_count
        else:
            rolling_slopeEMA[i] = np.nan

    # Initialize signals array
    total_signal = np.zeros(length)

    # Generate final signals
    for i in range(length):
        if np.isnan(rolling_slopeEMA[i]):
            continue

        price_range = high[i] - low[i]
        if price_range == 0:  # Avoid division by zero
            continue

        min_price = min(open[i], close[i])
        max_price = max(open[i], close[i])

        # Calculate retracement ratios
        lower_retracement = (min_price - low[i]) / price_range
        upper_retracement = (high[i] - max_price) / price_range

        if rolling_slopeEMA[i] < -slopelimit and lower_retracement > percentlimit:
            total_signal[i] = 1
        elif rolling_slopeEMA[i] > slopelimit and upper_retracement > percentlimit:
            total_signal[i] = 2

    return total_signal


# Inside The Alligator Trading Strategies With Advanced Analysis In Python
# https://www.youtube.com/watch?v=4rbGvaiD9Wk&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=67
def get_alligator_signals(symbol):
    tic = time.time()
    df = vbt.YFData.pull(symbols="NIO", start="2001-06-01", end="2024-04-14", timeframe="1 day").get()
    df[["Open", "High", "Low", "Close"]] = df[["Open", "High", "Low", "Close"]].round(3)

    df["RSI"] = pta.rsi(df.Close, length=16)
    df["ATR"] = pta.atr(df.High, df.Low, df.Close, length=7)

    df["SMA_1"] = pta.sma(df.Close, length=5).shift(3)  # 3
    df["SMA_2"] = pta.sma(df.Close, length=8).shift(5)  # 5
    df["SMA_3"] = pta.sma(df.Close, length=13).shift(8)  # 8
    df["SMA_Diff"] = df["SMA_1"] - df["SMA_3"]

    df["EMA"] = pta.ema(df.Close, length=200)

    def check_sma_conditions(row, df, n_backcandles):
        # Extract relevant slices of SMA columns for comparison
        sma_1_slice = df["SMA_1"].iloc[row.name - n_backcandles : row.name]
        sma_2_slice = df["SMA_2"].iloc[row.name - n_backcandles : row.name]
        sma_3_slice = df["SMA_3"].iloc[row.name - n_backcandles : row.name]
        sma_diff_slice = df["SMA_Diff"].iloc[row.name - n_backcandles : row.name]

        # Extract relevant slices of High, Low, and EMA columns for comparison
        high_slice = df["High"].iloc[row.name - n_backcandles : row.name]
        low_slice = df["Low"].iloc[row.name - n_backcandles : row.name]
        ema_slice = df["EMA"].iloc[row.name - n_backcandles : row.name]

        # Check conditions for all backcandles
        condition_1 = all(sma_1 < sma_2 < sma_3 for sma_1, sma_2, sma_3 in zip(sma_1_slice, sma_2_slice, sma_3_slice))
        condition_2 = all(sma_1 > sma_2 > sma_3 for sma_1, sma_2, sma_3 in zip(sma_1_slice, sma_2_slice, sma_3_slice))

        condition_1_confirmed = all(high < ema for high, ema in zip(high_slice, ema_slice))
        condition_2_confirmed = all(low > ema for low, ema in zip(low_slice, ema_slice))

        condition_3_average_sma_diff = abs(sma_diff_slice).mean() > 1e-6

        # Return the signal based on the conditions
        if condition_1 and condition_3_average_sma_diff and condition_2_confirmed:  # inverted conditions because alligator signal should happen ABOVE the EMA
            return 1
        if condition_2 and condition_3_average_sma_diff and condition_1_confirmed:  # inverted conditions because alligator signal should happen BELOW the EMA
            return 2

        return 0

    def assign_sma_signals(df, n_backcandles):
        # Ensure index is properly ordered and numeric for slicing
        df = df.reset_index(drop=False)

        # Initialize the SMA_Signal column with an apply function
        # Apply starts from the n_backcandles-th row to have enough data for comparison
        df["SMA_Signal"] = 0
        df.loc[n_backcandles:, "SMA_Signal"] = df.iloc[n_backcandles:].apply(check_sma_conditions, axis=1, args=(df, n_backcandles))

        return df

    n_backcandles = 10  # Number of backcandles to consider for the signal calculation
    df_with_signals = assign_sma_signals(df, n_backcandles)

    def total_signal(row):
        # Directly use the SMA_Signal value for the current row
        sma_signal_result = row["SMA_Signal"]

        # Calculate the candle's body and wick sizes
        # body_size = abs(row['Close'] - row['Open'])
        # upper_wick = max(row['High'] - row['Close'], row['High'] - row['Open'])

        # Check the conditions for generating the total signal
        if sma_signal_result == 2:
            if row["Close"] < row["SMA_2"]:  # and upper_wick < (body_size / 10)):
                return 1
        elif sma_signal_result == 1:
            if row["Close"] > row["SMA_2"]:  #  and upper_wick < (body_size / 10)):
                return 2

        return 0

    # Ensure the DataFrame has the 'SMA_Signal' column from running assign_sma_signals
    df = assign_sma_signals(df, n_backcandles)  # Assuming this has been done before

    # Apply the total_signal function to each row
    df["TotalSignal"] = df.apply(lambda row: total_signal(row) if row.name >= 7 else 0, axis=1)

    columns_to_retain = ["Open", "High", "Low", "Close", "RSI", "TotalSignal", "ATR"]
    df = df[columns_to_retain]
    df_column_names = df.columns.tolist()
    MyContext = namedtuple("MyContext", df_column_names + ["freq_in_minutes"])
    freq_in_minutes = 5
    my_context_data = tuple(df.to_numpy().T) + (freq_in_minutes,)
    my_context = MyContext._make(my_context_data)

    toc = time.time()

    rows_count = df.shape[0]
    signals_count = df["TotalSignal"].isin([1, 2]).sum()
    logging.info(f"row_count: {rows_count}, signls_count: {signals_count}, cost: {toc - tic:.2f}s")
    return df, my_context


# Automated Candlestick Strategy in Python | testing the shooting star
# https://www.youtube.com/watch?v=eN4zh3PEH6c&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=64
def get_shooting_star_signals():
    tic = time.time()

    df = vbt.YFData.pull(symbols="BABA", start="2001-06-01", end="2024-04-14", timeframe="1 day").get()
    df[["Open", "High", "Low", "Close"]] = df[["Open", "High", "Low", "Close"]].round(3)

    df["ATR"] = df.ta.atr(length=10)
    df["RSI"] = df.ta.rsi()

    def Revsignal1(df1):
        length = len(df1)
        high = list(df1["High"])
        low = list(df1["Low"])
        close = list(df1["Close"])
        open = list(df1["Open"])
        signal = [0] * length
        highdiff = [0] * length
        lowdiff = [0] * length
        bodydiff = [0] * length
        ratio1 = [0] * length
        ratio2 = [0] * length

        for row in range(0, length):
            highdiff[row] = high[row] - max(open[row], close[row])
            bodydiff[row] = abs(open[row] - close[row])
            if bodydiff[row] < 0.002:
                bodydiff[row] = 0.002
            lowdiff[row] = min(open[row], close[row]) - low[row]
            ratio1[row] = highdiff[row] / bodydiff[row]
            ratio2[row] = lowdiff[row] / bodydiff[row]

            # print(df.RSI[row])
            #  |
            # _|_
            # |__|
            # |
            #

            if ratio1[row] > 2.5 and lowdiff[row] < 0.3 * highdiff[row] and bodydiff[row] > 0.03 and df.RSI[row] > 50 and df.RSI[row] < 70:
                signal[row] = 1

            # elif (ratio2[row-1]>2.5 and highdiff[row-1]<0.23*lowdiff[row-1] and bodydiff[row-1]>0.03 and bodydiff[row]>0.04 and close[row]>open[row] and close[row]>high[row-1] and df.RSI[row]<55 and df.RSI[row]>30):
            #    signal[row] = 2
            # _|_
            # |__|
            # |
            # |

            elif ratio2[row] > 2.5 and highdiff[row] < 0.23 * lowdiff[row] and bodydiff[row] > 0.03 and df.RSI[row] < 55 and df.RSI[row] > 30:
                signal[row] = 2
        return signal

    df["TotalSignal"] = Revsignal1(df)

    def mytarget(barsupfront, df1):
        length = len(df1)
        high = list(df1["High"])
        low = list(df1["Low"])
        close = list(df1["Close"])
        open = list(df1["Open"])
        datr = list(df1["ATR"])
        trendcat = [0] * length

        for line in range(0, length - barsupfront - 1):
            valueOpenLow = 0
            valueOpenHigh = 0

            highdiff = high[line] - max(open[line], close[line])
            bodydiff = abs(open[line] - close[line])

            pipdiff = datr[line] * 1.0  # highdiff*1.3 #for SL 400*1e-3
            if pipdiff < 1.1:
                pipdiff = 1.1

            SLTPRatio = 2.0  # pipdiff*Ratio gives TP

            for i in range(1, barsupfront + 1):
                value1 = close[line] - low[line + i]
                value2 = close[line] - high[line + i]
                valueOpenLow = max(value1, valueOpenLow)
                valueOpenHigh = min(value2, valueOpenHigh)

                if (valueOpenLow >= (SLTPRatio * pipdiff)) and (-valueOpenHigh < pipdiff):
                    trendcat[line] = 1  # -1 downtrend
                    break
                elif ((valueOpenLow < pipdiff)) and (-valueOpenHigh >= (SLTPRatio * pipdiff)):
                    trendcat[line] = 2  # uptrend
                    break
                else:
                    trendcat[line] = 0  # no clear trend

        return trendcat

    # mytarget(barsfront to take into account, dataframe)
    df["Trend"] = mytarget(100, df)

    columns_to_retain = ["Open", "High", "Low", "Close", "RSI", "TotalSignal", "ATR"]
    df = df[columns_to_retain]
    df_column_names = df.columns.tolist()
    MyContext = namedtuple("MyContext", df_column_names + ["freq_in_minutes"])
    freq_in_minutes = 5
    my_context_data = tuple(df.to_numpy().T) + (freq_in_minutes,)
    my_context = MyContext._make(my_context_data)

    toc = time.time()

    rows_count = df.shape[0]
    signals_count = df["TotalSignal"].isin([1, 2]).sum()
    logging.info(f"row_count: {rows_count}, signls_count: {signals_count}, cost: {toc - tic:.2f}s")
    return df, my_context


# Engulfing Price Action Patterns Automated in Python
# https://www.youtube.com/watch?v=eN4zh3PEH6c&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=64
def get_engulfing_signals():
    tic = time.time()

    df = vbt.YFData.pull(symbols="BABA", start="2001-06-01", end="2024-04-14", timeframe="1 day").get()
    df[["Open", "High", "Low", "Close"]] = df[["Open", "High", "Low", "Close"]].round(3)

    df["ATR"] = df.ta.atr(length=10)
    df["RSI"] = df.ta.rsi()

    def Revsignal1(df1):
        length = len(df1)
        high = list(df1["High"])
        low = list(df1["Low"])
        close = list(df1["Close"])
        open = list(df1["Open"])
        signal = [0] * length
        bodydiff = [0] * length

        for row in range(1, length):
            bodydiff[row] = abs(open[row] - close[row])
            bodydiffmin = 0.003
            if (
                bodydiff[row] > bodydiffmin
                and bodydiff[row - 1] > bodydiffmin
                and open[row - 1] < close[row - 1]
                and open[row] > close[row]
                and
                # open[row]>=close[row-1] and close[row]<open[row-1]):
                (open[row] - close[row - 1]) >= +0e-5
                and close[row] < open[row - 1]
            ):
                signal[row] = 1
            elif (
                bodydiff[row] > bodydiffmin
                and bodydiff[row - 1] > bodydiffmin
                and open[row - 1] > close[row - 1]
                and open[row] < close[row]
                and
                # open[row]<=close[row-1] and close[row]>open[row-1]):
                (open[row] - close[row - 1]) <= -0e-5
                and close[row] > open[row - 1]
            ):
                signal[row] = 2
            else:
                signal[row] = 0
            # signal[row]=random.choice([0, 1, 2])
            # signal[row]=1
        return signal

    df["TotalSignal"] = Revsignal1(df)

    def mytarget(df1, barsfront):
        length = len(df1)
        high = list(df1["High"])
        low = list(df1["Low"])
        close = list(df1["Close"])
        open = list(df1["Open"])
        trendcat = [None] * length

        piplim = 300e-5
        for line in range(0, length - 1 - barsfront):
            for i in range(1, barsfront + 1):
                if ((high[line + i] - max(close[line], open[line])) > piplim) and ((min(close[line], open[line]) - low[line + i]) > piplim):
                    trendcat[line] = 3  # no trend
                elif (min(close[line], open[line]) - low[line + i]) > piplim:
                    trendcat[line] = 1  # -1 downtrend
                    break
                elif (high[line + i] - max(close[line], open[line])) > piplim:
                    trendcat[line] = 2  # uptrend
                    break
                else:
                    trendcat[line] = 0  # no clear trend
        return trendcat

    df["Trend"] = mytarget(df, 3)

    columns_to_retain = ["Open", "High", "Low", "Close", "RSI", "TotalSignal", "ATR"]
    df = df[columns_to_retain]
    df_column_names = df.columns.tolist()
    MyContext = namedtuple("MyContext", df_column_names + ["freq_in_minutes"])
    freq_in_minutes = 5
    my_context_data = tuple(df.to_numpy().T) + (freq_in_minutes,)
    my_context = MyContext._make(my_context_data)

    toc = time.time()

    rows_count = df.shape[0]
    signals_count = df["TotalSignal"].isin([1, 2]).sum()
    logging.info(f"row_count: {rows_count}, signls_count: {signals_count}, cost: {toc - tic:.2f}s")
    return df, my_context


# Automated Price Action Patterns Analysis In Python
# https://www.youtube.com/watch?v=TiWjTpuL21w&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=62
def get_price_action_signals():
    tic = time.time()

    df = vbt.YFData.pull(symbols="BABA", start="2001-06-01", end="2024-04-14", timeframe="1 day").get()
    df[["Open", "High", "Low", "Close"]] = df[["Open", "High", "Low", "Close"]].round(3)

    df["ATR"] = df.ta.atr(length=10)
    df["RSI"] = df.ta.rsi()

    length = len(df)
    high = list(df["High"])
    low = list(df["Low"])
    close = list(df["Close"])
    open = list(df["Open"])
    bodydiff = [0] * length

    highdiff = [0] * length
    lowdiff = [0] * length
    ratio1 = [0] * length
    ratio2 = [0] * length

    def isEngulfing(l):
        row = l
        bodydiff[row] = abs(open[row] - close[row])
        if bodydiff[row] < 0.000001:
            bodydiff[row] = 0.000001

        bodydiffmin = 0.002
        if bodydiff[row] > bodydiffmin and bodydiff[row - 1] > bodydiffmin and open[row - 1] < close[row - 1] and open[row] > close[row] and (open[row] - close[row - 1]) >= -0e-5 and close[row] < open[row - 1]:  # +0e-5 -5e-5
            return 1

        elif bodydiff[row] > bodydiffmin and bodydiff[row - 1] > bodydiffmin and open[row - 1] > close[row - 1] and open[row] < close[row] and (open[row] - close[row - 1]) <= +0e-5 and close[row] > open[row - 1]:  # -0e-5 +5e-5
            return 2
        else:
            return 0

    def isEngulfingStrong(l):
        row = l
        bodydiff[row] = abs(open[row] - close[row])
        if bodydiff[row] < 0.000001:
            bodydiff[row] = 0.000001

        bodydiffmin = 0.002
        if bodydiff[row] > bodydiffmin and bodydiff[row - 1] > bodydiffmin and open[row - 1] < close[row - 1] and open[row] > close[row] and (open[row] - close[row - 1]) >= -0e-5 and close[row] < low[row - 1]:  # +0e-5 -5e-5
            return 1

        elif bodydiff[row] > bodydiffmin and bodydiff[row - 1] > bodydiffmin and open[row - 1] > close[row - 1] and open[row] < close[row] and (open[row] - close[row - 1]) <= +0e-5 and close[row] > high[row - 1]:  # -0e-5 +5e-5
            return 2
        else:
            return 0

    def isStar(l):
        bodydiffmin = 0.0020
        row = l
        highdiff[row] = high[row] - max(open[row], close[row])
        lowdiff[row] = min(open[row], close[row]) - low[row]
        bodydiff[row] = abs(open[row] - close[row])
        if bodydiff[row] < 0.000001:
            bodydiff[row] = 0.000001
        ratio1[row] = highdiff[row] / bodydiff[row]
        ratio2[row] = lowdiff[row] / bodydiff[row]

        if ratio1[row] > 1 and lowdiff[row] < 0.2 * highdiff[row] and bodydiff[row] > bodydiffmin:  # and open[row]>close[row]):
            return 1
        elif ratio2[row] > 1 and highdiff[row] < 0.2 * lowdiff[row] and bodydiff[row] > bodydiffmin:  # and open[row]<close[row]):
            return 2
        else:
            return 0

    def direction(l):
        if open[l] > close[l]:
            return 1
        elif open[l] < close[l]:
            return 2
        else:
            return 0

    def Revsignal1():
        signal = [0] * length
        for row in range(1, length):
            if isEngulfing(row) == 1 and isStar(row) == 1:  # and df.RSI[row]<30
                signal[row] = 1
            elif isEngulfing(row) == 2 and isStar(row) == 2:  # and df.RSI[row]>70
                signal[row] = 2
            else:
                signal[row] = 0
        return signal

    df["TotalSignal"] = Revsignal1()

    def mytarget(df1, barsfront):
        length = len(df1)
        high = list(df1["High"])
        low = list(df1["Low"])
        close = list(df1["Close"])
        open = list(df1["Open"])
        trendcat = [None] * length

        piplim = 200e-5
        for line in range(0, length - 1 - barsfront):
            for i in range(1, barsfront + 1):
                if ((high[line + i] - close[line]) > piplim) and ((close[line] - low[line + i]) > piplim):
                    trendcat[line] = 3  # no trend
                    break
                elif (close[line] - low[line + i]) > piplim:
                    trendcat[line] = 1  # -1 downtrend
                    break
                elif (high[line + i] - close[line]) > piplim:
                    trendcat[line] = 2  # uptrend
                    break
                else:
                    trendcat[line] = 0  # no clear trend
        return trendcat

    df["Trend"] = mytarget(df, 10)

    columns_to_retain = ["Open", "High", "Low", "Close", "RSI", "TotalSignal", "ATR"]
    df = df[columns_to_retain]
    df_column_names = df.columns.tolist()
    MyContext = namedtuple("MyContext", df_column_names + ["freq_in_minutes"])
    freq_in_minutes = 5
    my_context_data = tuple(df.to_numpy().T) + (freq_in_minutes,)
    my_context = MyContext._make(my_context_data)

    toc = time.time()

    rows_count = df.shape[0]
    signals_count = df["TotalSignal"].isin([1, 2]).sum()
    logging.info(f"row_count: {rows_count}, signls_count: {signals_count}, cost: {toc - tic:.2f}s")
    return df, my_context


# Price Trend Channels Automated In Python
# https://www.youtube.com/watch?v=phy57DZOIwc&list=PLwEOixRFAUxZmM26EYI1uYtJG39HDW1zm&index=57
def get_price_trend_signals():
    tic = time.time()

    df = vbt.YFData.pull(symbols="BABA", start="2001-06-01", end="2024-04-14", timeframe="1 day").get()
    df[["Open", "High", "Low", "Close"]] = df[["Open", "High", "Low", "Close"]].round(3)

    df["ATR"] = df.ta.atr(length=10)
    df["RSI"] = df.ta.rsi()

    columns_to_retain = ["Open", "High", "Low", "Close", "RSI", "TotalSignal", "ATR"]
    df = df[columns_to_retain]
    df_column_names = df.columns.tolist()
    MyContext = namedtuple("MyContext", df_column_names + ["freq_in_minutes"])
    freq_in_minutes = 5
    my_context_data = tuple(df.to_numpy().T) + (freq_in_minutes,)
    my_context = MyContext._make(my_context_data)

    toc = time.time()

    rows_count = df.shape[0]
    signals_count = df["TotalSignal"].isin([1, 2]).sum()
    logging.info(f"row_count: {rows_count}, signls_count: {signals_count}, cost: {toc - tic:.2f}s")
    return df, my_context


def test_signals_match(base_func_name: str):
    """Test if signals from pandas implementation match numba implementation.

    Args:
        base_func_name: Base name of the function pair (e.g. 'vwap_bbands')
    """
    # Get the pandas implementation
    pandas_func = getattr(strategies, base_func_name)
    # Get the numba implementation
    numba_func = getattr(strategies, f"{base_func_name}_nb")

    # Time pandas signals
    df = get_data(as_namedtuple=False)
    pandas_start = time.time()
    df_signals = pandas_func(df)
    pandas_signals = df_signals["TotalSignal"].values
    pandas_time = time.time() - pandas_start

    # Time numba signals
    data_context = strategies.get_data(as_namedtuple=True)
    numba_start = time.time()
    numba_signals = numba_func(data_context)
    numba_time = time.time() - numba_start

    signals_match = np.array_equal(pandas_signals, numba_signals)

    if not signals_match:
        diff_indices = np.where(pandas_signals != numba_signals)[0]
        for idx in diff_indices:
            # fmt: off
            OpenTime = pd.Timestamp(data_context.OpenTime[idx]).strftime('%Y-%m-%d %H:%M:%S')
            candle_data = {
                'OpenTime': OpenTime,
                'Open': data_context.Open[idx],
                'High': data_context.High[idx],
                'Low': data_context.Low[idx],
                'Close': data_context.Close[idx],
                'Volume': data_context.Volume[idx]
            }

            logging.error(f"\n"
                          f"diff at index {idx}:\n"
                          f"Pandas signal: {pandas_signals[idx]}\n"
                          f"Numba signal: {numba_signals[idx]}\n"
                          f"Candle data:\n"
                          f"  Timestamp: {candle_data['OpenTime']}\n"
                          f"  Open: {candle_data['Open']:.6f}\n"
                          f"  High: {candle_data['High']:.6f}\n"
                          f"  Low: {candle_data['Low']:.6f}\n"
                          f"  Close: {candle_data['Close']:.6f}\n"
                          f"  Volume: {candle_data['Volume']:.1f}"
                          )
            # fmt: on
            break

    # Additional validation of signal values
    unique_signals = np.unique(pandas_signals)
    assert all(signal in [0, 1, 2] for signal in unique_signals), f"Invalid signal values found: {unique_signals}"

    signal_counts = {
        0: np.sum(numba_signals == 0),  # No signal
        1: np.sum(numba_signals == 1),  # Sell signals
        2: np.sum(numba_signals == 2),  # Buy signals
    }

    # fmt: off
    rows_count = data_context.Close.shape[0]
    diff_count = np.sum(pandas_signals != numba_signals)
    logging.info(f"\n"
                 f"TotalBars: {rows_count}\n"
                 f"Different signals: {diff_count}\n\n"
                 f"No signals: {signal_counts[0]}\n"
                 f"Sell signals: {signal_counts[1]}\n"
                 f"Buy signals: {signal_counts[2]}\n"
                 f"Pandas time: {pandas_time:.3f}s\n"
                 f"Numba time: {numba_time:.3f}s\n"
                 )
    # fmt: on

def test_signals_logic():
    # test_signals_match("vwap_bbands")
    test_signals_match("get_ema_bbands_signals")  # issues: assert abs(next_potential_order_size - position_changed) < 1e-5
    # test_signals_match("get_rsi_ema_scalping_signals")
    # test_signals_match("get_candlestick_scalping_signals")
    # test_signals_match("get_ema_scalping_signals")
    # test_signals_match("get_vwap_scalping_signals")
    # test_signals_match("get_price_action_scalping_signals")
    # test_signals_match("get_adx_scalping_signals")  # Numba version is OK
    # test_signals_match("get_retracement_signals")

def handle():
    # Get data as DataFrame for both strategies
    df = get_data(as_namedtuple=False)
    
    # Apply both strategies
    df_jma = get_jma_bbands_signals(df.copy())
    df_ema = get_rsi_ema_scalping_signals(df.copy())
    
    jma_signals = df_jma["TotalSignal"].values
    ema_signals = df_ema["TotalSignal"].values
    
    # Count signals for both strategies
    jma_signal_counts = {
        0: np.sum(jma_signals == 0),  # No signal
        1: np.sum(jma_signals == 1),  # Sell signals
        2: np.sum(jma_signals == 2),  # Buy signals
    }
    
    ema_signal_counts = {
        0: np.sum(ema_signals == 0),  # No signal
        1: np.sum(ema_signals == 1),  # Sell signals
        2: np.sum(ema_signals == 2),  # Buy signals
    }
    
    # Analyze signal correctness
    def analyze_signal_correctness(df_signals, signals):
        """
        Analyze signal correctness based on actual trading logic.
        
        When signal is detected at barIndex=i, order is placed at next bar's open (i+1).
        Success criteria:
        - Long: next_close > next_open AND close[i+2] > close[i+1]
        - Short: next_close < next_open AND close[i+2] < close[i+1]
        
        Args:
            df_signals: DataFrame with signals and OHLC data
            signals: Array of signal values
            lookforward_candles: Number of candles to look ahead for validation (should be 2)
        
        Returns:
            Dictionary with correctness statistics
        """
        correct_long = 0
        correct_short = 0
        total_long = 0
        total_short = 0
        
        # Need at least 2 bars after signal for validation
        for i in range(len(signals) - 2):
            signal = signals[i]
            
            if signal == 2:  # Long entry signal
                total_long += 1
                
                # Order placed at next bar's open (i+1)
                next_open = df_signals.iloc[i + 1]['Open']
                next_close = df_signals.iloc[i + 1]['Close']
                
                # Check if we have the second bar for confirmation
                if i + 2 < len(df_signals):
                    second_close = df_signals.iloc[i + 2]['Close']
                    
                    # Success criteria for long:
                    # 1. Next bar is bullish (close > open)
                    # 2. Price continues higher (second_close > next_close)
                    if next_close > next_open and second_close > next_close:
                        correct_long += 1
                    
            elif signal == 1:  # Short entry signal
                total_short += 1
                
                # Order placed at next bar's open (i+1)
                next_open = df_signals.iloc[i + 1]['Open']
                next_close = df_signals.iloc[i + 1]['Close']
                
                # Check if we have the second bar for confirmation
                if i + 2 < len(df_signals):
                    second_close = df_signals.iloc[i + 2]['Close']
                    
                    # Success criteria for short:
                    # 1. Next bar is bearish (close < open)
                    # 2. Price continues lower (second_close < next_close)
                    if next_close < next_open and second_close < next_close:
                        correct_short += 1
        
        return {
            'total_long': total_long,
            'correct_long': correct_long,
            'total_short': total_short,
            'correct_short': correct_short,
            'long_accuracy': correct_long / total_long if total_long > 0 else 0,
            'short_accuracy': correct_short / total_short if total_short > 0 else 0,
            'overall_accuracy': (correct_long + correct_short) / (total_long + total_short) if (total_long + total_short) > 0 else 0
        }
    
    # Analyze signal correctness for both strategies
    jma_correctness_2 = analyze_signal_correctness(df_jma, jma_signals)
    
    ema_correctness_2 = analyze_signal_correctness(df_ema, ema_signals)
    
    rows_count = len(df)
    
    # Display comprehensive comparison
    logging.info(f"\n"
                 f"{'='*80}\n"
                 f"STRATEGY COMPARISON: JMA vs EMA Bollinger Bands\n"
                 f"{'='*80}\n"
                 f"Total Bars: {rows_count}\n"
                 f"\n"
                 f"{'SIGNAL COUNTS':<40} {'JMA':<15} {'EMA':<15}\n"
                 f"{'-'*70}\n"
                 f"{'No signals':<40} {jma_signal_counts[0]:<15} {ema_signal_counts[0]:<15}\n"
                 f"{'Sell signals':<40} {jma_signal_counts[1]:<15} {ema_signal_counts[1]:<15}\n"
                 f"{'Buy signals':<40} {jma_signal_counts[2]:<15} {ema_signal_counts[2]:<15}\n"
                 f"{'Total signals':<40} {jma_signal_counts[1] + jma_signal_counts[2]:<15} {ema_signal_counts[1] + ema_signal_counts[2]:<15}\n"
                 f"\n"
                 f"ACCURACY COMPARISON (2 Candles Lookforward)\n"
                 f"{'-'*70}\n"
                 f"{'Long Accuracy':<40} {jma_correctness_2['long_accuracy']:<15.2%} {ema_correctness_2['long_accuracy']:<15.2%}\n"
                 f"{'Short Accuracy':<40} {jma_correctness_2['short_accuracy']:<15.2%} {ema_correctness_2['short_accuracy']:<15.2%}\n"
                 f"{'Overall Accuracy':<40} {jma_correctness_2['overall_accuracy']:<15.2%} {ema_correctness_2['overall_accuracy']:<15.2%}\n"
                 f"{'Long Signals (correct/total)':<40} {jma_correctness_2['correct_long']}/{jma_correctness_2['total_long']:<15} {ema_correctness_2['correct_long']}/{ema_correctness_2['total_long']:<15}\n"
                 f"{'Short Signals (correct/total)':<40} {jma_correctness_2['correct_short']}/{jma_correctness_2['total_short']:<15} {ema_correctness_2['correct_short']}/{ema_correctness_2['total_short']:<15}\n"
                 f"\n"
                 f"PERFORMANCE SUMMARY\n"
                 f"{'-'*70}\n"
                 f"{'Best 2-Candle Accuracy:':<40} {'JMA' if jma_correctness_2['overall_accuracy'] > ema_correctness_2['overall_accuracy'] else 'EMA' if ema_correctness_2['overall_accuracy'] > jma_correctness_2['overall_accuracy'] else 'TIE':<15}\n"
                 f"{'More Signals Generated:':<40} {'JMA' if (jma_signal_counts[1] + jma_signal_counts[2]) > (ema_signal_counts[1] + ema_signal_counts[2]) else 'EMA' if (ema_signal_counts[1] + ema_signal_counts[2]) > (jma_signal_counts[1] + jma_signal_counts[2]) else 'TIE':<15}\n"
                 f"{'='*80}\n"
                 )
    # Choose which strategy to plot (you can change this to compare visually)
    plot_jma = False  # Set to True for JMA, False for EMA
    return
    if plot_jma:
        df_plot = df_jma.copy()
        strategy_name = "JMA"
        ma_fast_col = 'JMA_fast'
        ma_slow_col = 'JMA_slow'
        ma_fast_color = 'black'
        ma_slow_color = 'blue'
    else:
        df_plot = df_ema.copy()
        strategy_name = "EMA"
        ma_fast_col = 'EMA_fast'
        ma_slow_col = 'EMA_slow'
        ma_fast_color = 'orange'
        ma_slow_color = 'purple'
    
    # Create pointpos column for signal markers
    def pointpos(x):
        if x['TotalSignal'] == 2:
            return x['Low'] - 1e-3
        elif x['TotalSignal'] == 1:
            return x['High'] + 1e-3
        else:
            return np.nan
    
    df_plot['pointpos'] = df_plot.apply(lambda row: pointpos(row), axis=1)
    
    # Select data range for plotting
    st = 100
    dfpl = df_plot[st:st+350]
    
    # Create the plot
    fig = go.Figure(data=[go.Candlestick(x=dfpl.index,
                    open=dfpl['Open'],
                    high=dfpl['High'],
                    low=dfpl['Low'],
                    close=dfpl['Close'],
                    name="Candlestick"),

                    go.Scatter(x=dfpl.index, y=dfpl['BBL_13_1.1'], 
                               line=dict(color='green', width=1), 
                               name="BBL"),
                    go.Scatter(x=dfpl.index, y=dfpl['BBU_13_1.1'], 
                               line=dict(color='green', width=1), 
                               name="BBU"),
                    go.Scatter(x=dfpl.index, y=dfpl[ma_fast_col], 
                               line=dict(color=ma_fast_color, width=1), 
                               name=f"{strategy_name}_fast"),
                    go.Scatter(x=dfpl.index, y=dfpl[ma_slow_col], 
                               line=dict(color=ma_slow_color, width=1), 
                               name=f"{strategy_name}_slow")])

    # Add separate scatter plots for buy and sell signals
    # Long entries (buy signals = 2) in Green
    long_mask = dfpl['TotalSignal'] == 2
    if long_mask.any():
        fig.add_scatter(x=dfpl[long_mask].index, y=dfpl[long_mask]['pointpos'], 
                        mode="markers",
                        marker=dict(size=6, color="Green", symbol="triangle-up"),
                        name="Long Entry")
    
    # Short entries (sell signals = 1) in Red  
    short_mask = dfpl['TotalSignal'] == 1
    if short_mask.any():
        fig.add_scatter(x=dfpl[short_mask].index, y=dfpl[short_mask]['pointpos'], 
                        mode="markers",
                        marker=dict(size=6, color="Red", symbol="triangle-down"),
                        name="Short Entry")
    
    # Update layout
    fig.update_layout(
        title=f'Trading Signals with {strategy_name} and Bollinger Bands',
        yaxis_title='Price',
        xaxis_title='Time',
        template='plotly_white',
        height=600,
        showlegend=True,
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        )
    )
    
    fig.show()


def main():
    if sys.platform == "win32":
        os.system("cls")
    else:
        os.system("clear")

    t0 = pendulum.now()

    utils.init_logging()

    handle()

    logging.info("-" * 60)
    t1 = pendulum.now()
    delta = t1 - t0
    logging.info(f"elapsed time: `{delta.in_words(locale='en')}`")


if __name__ == "__main__":
    main()
