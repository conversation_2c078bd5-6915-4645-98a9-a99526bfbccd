# fmt: off
import os
import sys
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
import utils
# fmt: on

from aligo import Aligo
import datetime as dt
import logging
import math
import subprocess
import threading
import time
from concurrent.futures import thread
import pandas as pd
import pandas_ta as ta
import strategies
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
import numpy as np
import pandas as pd
from numba import njit
import vectorbtpro as vbt
from collections import namedtuple
import pandas as pd
import pendulum
import vbt_custom_simulation


def handle():
    df, not_used_context = strategies.vwap_bbands()
    MyContext = namedtuple(
        "MyContext",
        [
            "TotalSignal",
            "Close",
            "RSI",
            "ATR",
        ],
    )

    my_context_template = vbt.RepEval(
        "MyContext(TotalSignal, Close, RSI, ATR)",
        context=dict(MyContext=MyContext),
    )  # template to create named tuple once all arrays are ready

    @njit
    def signal_func_nb(c, my_context):
        if c.i == 0:
            return False, False, False, False

        cur_signal = vbt.pf_nb.select_nb(c, my_context.TotalSignal, i=c.i - 1)
        cur_close = vbt.pf_nb.select_nb(c, my_context.Close, i=c.i - 1)
        cur_RSI = vbt.pf_nb.select_nb(c, my_context.RSI, i=c.i - 1)
        cur_ATR = vbt.pf_nb.select_nb(c, my_context.ATR, i=c.i - 1)

        pos_info = c.last_pos_info[c.col]
        trade_open = pos_info["status"] == vbt.pf_enums.TradeStatus.Open
        if cur_signal == 2 and not trade_open:
            return True, False, False, False
        elif cur_signal == 1 and not trade_open:
            return False, False, True, False
        return False, False, False, False

    @njit
    def post_signal_func_nb(c, my_context):  # called after signal has been executed
        if not vbt.pf_nb.order_opened_position_nb(c):  # set stop once new position opened
            return

        cur_ATR = vbt.pf_nb.select_nb(c, my_context.ATR, i=c.i - 1)
        cur_close = vbt.pf_nb.select_nb(c, my_context.Close, i=c.i - 1)
        slatr = 1.2 * cur_ATR
        TPSLRatio = 1.5
        sl_info = c.last_sl_info[c.col]
        if vbt.pf_nb.in_long_position_nb(c):
            sl1 = cur_close - slatr
            tp1 = cur_close + slatr * TPSLRatio
        elif vbt.pf_nb.in_short_position_nb(c):
            sl1 = cur_close + slatr
            tp1 = cur_close - slatr * TPSLRatio
        if np.isinf(sl_info["stop"]):
            sl_info["delta_format"] = vbt.pf_enums.DeltaFormat.Target  # ATR-based stop is not %
            sl_info["stop"] = sl1
        tp_info = c.last_tp_info[c.col]
        if np.isinf(tp_info["stop"]):
            tp_info["delta_format"] = vbt.pf_enums.DeltaFormat.Target  # ATR-based stop is not %
            tp_info["stop"] = tp1

    tic = time.time()
    pf = vbt.PF.from_signals(
        open=df.Open,
        high=df.High,
        low=df.Low,
        close=df.Close,
        signal_func_nb=signal_func_nb,
        signal_args=(my_context_template,),
        post_signal_func_nb=post_signal_func_nb,
        post_signal_args=(my_context_template,),
        broadcast_named_args=dict(
            Close=df.Close,
            TotalSignal=df.TotalSignal,
            RSI=df.RSI,
            ATR=df.ATR,
        ),
        direction="both",
        init_cash=100,
        leverage=10.0,
        leverage_mode="Lazy",
        fees=None,
        size_granularity=1.0,
        size=0.99,
        size_type="percent",
        log=True,
        sl_stop=np.inf,  # activate SL but set the value dynamically
        tp_stop=np.inf,  # activate TP but set the value dynamically
        freq="5m",
        price="open",
        reject_prob=0.0,
        save_state=True,
        save_value=True,
        save_returns=True,
        cash_sharing=False,
    )
    toc = time.time()
    logging.info(f"vbt.PF.from_signals cost: {toc - tic:.2f}s")
    logging.info(f"pf.stats: \n{pf.stats()}")
    # logging.info(f"pf.orders.records_readable:\n{pf.orders.records_readable}")
    # logging.info(f"pf.orders.values:\n{pf.orders.values}")
    # logging.info(f"pf.trades.records_readable:\n{pf.trades.records_readable}")
    pf_trades_values = pd.DataFrame(pf.trades.values)
    pf_trades_values["pnl"] = pf_trades_values["pnl"].apply(lambda x: f"{x:0.3f}")
    # logging.info(f"pf_trades_values:\n{pf_trades_values}")
    # logging.info(f"pf.trade_history: \n{pf.trade_history}")
    # logging.info(f"pf.returns: {pf.get_returns()}")
    # value = pf.value.apply(lambda x: format(x, '.2f'))
    # logging.info(f"\n{value}")

    logging.info(rf"Max Drawdown Duration: {pf.drawdowns.max_duration}")
    logging.info(rf"Win Rate [%]: {pf.trades.status_closed.win_rate * 100: .2f}%")
    logging.info(rf"Max Drawdown [%]: {pf.drawdowns.max_drawdown * 100: .2f}%")

    ratio = 2
    vbt.settings.set_theme("dark")
    vbt.settings["plotting"]["layout"]["width"] = 700 * ratio
    vbt.settings["plotting"]["layout"]["height"] = 350 * ratio

    total_bars = df.shape[0]
    pf.iloc[0:total_bars].plot().auto_rangebreaks().show()
    # pf.iloc[0:total_bars].plot_trade_signals().auto_rangebreaks().show()
    # pf.trades.plot().auto_rangebreaks().show()
    # pf.iloc[0:total_bars].plot_cum_returns().show()
    # pf.iloc[0:total_bars].trades.crop().plot().auto_rangebreaks().show()
    # pf.iloc[0:total_bars].orders.plot().auto_rangebreaks().show()


def main():
    if sys.platform == "win32":
        os.system("cls")
    else:
        os.system("clear")

    t0 = pendulum.now()

    utils.init_logging()

    handle()

    logging.info("-" * 60)
    t1 = pendulum.now()
    delta = t1 - t0
    logging.info(f"elapsed time: `{delta.in_words(locale='en')}`")

    print("about to exit~")


if __name__ == "__main__":
    main()
