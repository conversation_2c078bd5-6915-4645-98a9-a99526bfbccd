import pandas as pd
import pandas_ta as pta
from dataclasses import dataclass
import talib
from datetime import timedelta
import hexital
import math
import json
import pickle
import copy
import inspect
import os

pd.options.display.max_columns = None
pd.options.display.max_rows = None
pd.options.display.width = None
pd.options.display.precision = 10
pd.options.display.unicode.east_asian_width = True

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
data_source = parentdir + r"/data/DOGFDUSD_Candlestick_(5m)_(2024-06-24-2024-06-26).csv"
df = pd.read_csv(data_source, parse_dates=True)

df.rename(columns={"OpenTime": "timestamp"}, inplace=True)
df.columns = df.columns.str.lower()
ohlcv_dicts = df.to_dict(orient="records")


# necessary to set index as datetime for pandas_ta
df["timestamp"] = pd.to_datetime(df["timestamp"])
df.set_index("timestamp", inplace=True)

# Convert the columns to NumPy arrays
open = df["open"].to_numpy()
high = df["high"].to_numpy()
low = df["low"].to_numpy()
close = df["close"].to_numpy()
volume = df["volume"].to_numpy()

warmup_len = 7
decimal_places = 10

pta_atr = pta.atr(df.high, df.low, df.close, length=warmup_len).round(decimal_places).to_list()
pta_vwap = pta.vwap(df.high, df.low, df.close, df.volume).round(decimal_places).to_list()
pta_rsi = pta.rsi(df.close, warmup_len).round(decimal_places).to_list()
pta_sma = pta.sma(df.close, warmup_len).round(decimal_places).to_list()
pta_ema = pta.ema(df.close, warmup_len).round(decimal_places).to_list()
pta_wma = pta.wma(df.close, warmup_len).round(decimal_places).to_list()
pta_rma = pta.rma(df.close, warmup_len).round(decimal_places).to_list()
pta_vwma = pta.vwma(df.close, df.volume, warmup_len).round(decimal_places).to_list()

pta_adx = pta.adx(df.high, df.low, df.close, length=warmup_len, lensig=warmup_len)
pta_adx = pta_adx.round(decimal_places)
ta_adx = talib.ADX(df.high, df.low, df.close, warmup_len)


pta_bbands = pta.bbands(df.close, length=warmup_len, std=2.0)
pta_bbands = pta_bbands.round(decimal_places)

candles_lifespan = timedelta(minutes=(3 * warmup_len) * 5)
hexital_atr_obj = hexital.ATR(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_vwap_obj = hexital.VWAP(rounding=decimal_places, candle_life=candles_lifespan)
hexital_rsi_obj = hexital.RSI(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_bbands_obj = hexital.BBANDS(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_sma_obj = hexital.SMA(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_ema_obj = hexital.EMA(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_wma_obj = hexital.WMA(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_rma_obj = hexital.RMA(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_vwma_obj = hexital.VWMA(period=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)
hexital_adx_obj = hexital.ADX(period=warmup_len, period_signal=warmup_len, rounding=decimal_places, candle_life=candles_lifespan)


for index, ohlcv_dict in enumerate(ohlcv_dicts):
    candle = hexital.Candle.from_dict(ohlcv_dict)
    print(f"checking {index}~")

    # BANDS
    hexital_bbands_obj.append(candle)
    hexital_bbands_val = hexital_bbands_obj.reading()
    hexital_bbands_bbl = hexital_bbands_val["BBL"]
    hexital_bbands_bbm = hexital_bbands_val["BBM"]
    hexital_bbands_bbu = hexital_bbands_val["BBU"]
    if all([hexital_bbands_bbl, hexital_bbands_bbm, hexital_bbands_bbu]):
        assert math.isclose(hexital_bbands_bbl, pta_bbands.iloc[index][f"BBL_{warmup_len}_2.0"], abs_tol=1e-9)
        assert math.isclose(hexital_bbands_bbm, pta_bbands.iloc[index][f"BBM_{warmup_len}_2.0"], abs_tol=1e-9)
        assert math.isclose(hexital_bbands_bbu, pta_bbands.iloc[index][f"BBU_{warmup_len}_2.0"], abs_tol=1e-9)

    # RSI
    hexital_rsi_obj.append(candle)
    hexital_rsi_val = hexital_rsi_obj.reading()
    if hexital_rsi_val is not None:
        assert math.isclose(hexital_rsi_val, pta_rsi[index], abs_tol=1e-9)

    # VWAP
    hexital_vwap_obj.append(candle)
    hexital_vwap_val = hexital_vwap_obj.reading()
    if hexital_vwap_val is not None:
        assert math.isclose(hexital_vwap_val, pta_vwap[index], abs_tol=1e-9)
        ...

    # ATR
    hexital_atr_obj.append(candle)
    hexital_atr_val = hexital_atr_obj.reading()
    if hexital_atr_val is not None:
        assert math.isclose(hexital_atr_val, pta_atr[index], abs_tol=1e-9)

    # ADX
    hexital_adx_obj.append(candle)
    hexital_adx_val = hexital_adx_obj.reading()
    hexital_adx_adx = hexital_adx_val["ADX"]
    hexital_adx_dmp = hexital_adx_val["DM_Plus"]
    hexital_adx_dmn = hexital_adx_val["DM_Neg"]
    if all([hexital_adx_adx, hexital_adx_dmp, hexital_adx_dmn]):
        # assert math.isclose(hexital_adx_adx, pta_adx.iloc[index][f"ADX_{len}"], abs_tol=1e-9)
        # assert math.isclose(hexital_adx_dmp, pta_adx.iloc[index][f"DMP_{len}"], abs_tol=1e-9)
        # assert math.isclose(hexital_adx_dmn, pta_adx.iloc[index][f"DMN_{len}"], abs_tol=1e-9)
        ...

    # SMA
    hexital_sma_obj.append(candle)
    hexital_sma_val = hexital_sma_obj.reading()
    if hexital_sma_val is not None:
        assert math.isclose(hexital_sma_val, pta_sma[index], abs_tol=1e-9)

    # EMA
    hexital_ema_obj.append(candle)
    hexital_ema_val = hexital_ema_obj.reading()
    if hexital_ema_val is not None:
        assert math.isclose(hexital_ema_val, pta_ema[index], abs_tol=1e-9)

    # WMA
    hexital_wma_obj.append(candle)
    hexital_wma_val = hexital_wma_obj.reading()
    if hexital_wma_val is not None:
        assert math.isclose(hexital_wma_val, pta_wma[index], abs_tol=1e-9)

    # RMA
    hexital_rma_obj.append(candle)
    hexital_rma_val = hexital_rma_obj.reading()
    if hexital_rma_val is not None:
        # pandas is calculating wrong
        # assert math.isclose(hexital_rma_val, pta_rma[index], abs_tol=1e-9)
        ...

    # VMWA
    hexital_vwma_obj.append(candle)
    hexital_vwma_val = hexital_vwma_obj.reading()
    if hexital_vwma_val is not None:
        assert math.isclose(hexital_vwma_val, pta_vwma[index], abs_tol=1e-9)
