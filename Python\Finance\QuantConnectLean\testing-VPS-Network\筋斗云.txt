(base) root@C20240728121274:~# ab -n 100 -c 10 "https://api.binance.com/api/v3/ticker/price?symbol=BNBBTC"
This is ApacheBench, Version 2.3 <$Revision: 1903618 $>
Copyright 1996 <PERSON>, Zeus Technology Ltd, http://www.zeustech.net/
Licensed to The Apache Software Foundation, http://www.apache.org/

Benchmarking api.binance.com (be patient).....done


Server Software:        nginx
Server Hostname:        api.binance.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_128_GCM_SHA256,2048,128
Server Temp Key:        X25519 253 bits
TLS Server Name:        api.binance.com

Document Path:          /api/v3/ticker/price?symbol=BNBBTC
Document Length:        40 bytes

Concurrency Level:      10
Time taken for tests:   5.843 seconds
Complete requests:      100
Failed requests:        0
Total transferred:      94194 bytes
HTML transferred:       4000 bytes
Requests per second:    17.12 [#/sec] (mean)
Time per request:       584.256 [ms] (mean)
Time per request:       58.426 [ms] (mean, across all concurrent requests)
Transfer rate:          15.74 [Kbytes/sec] received

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:      220  275  97.9    237     500
Processing:   213  245  53.1    223     429
Waiting:      213  245  53.2    223     429
Total:        435  521 147.5    461     859

Percentage of the requests served within a certain time (ms)
  50%    461
  66%    463
  75%    467
  80%    470
  90%    852
  95%    853
  98%    855
  99%    859
100%    859 (longest request)
