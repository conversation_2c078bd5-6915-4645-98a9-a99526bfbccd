/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
*/

using System.Collections.Generic;
using System.Linq;
using QuantConnect.Data;
using QuantConnect.Interfaces;
using QuantConnect.Securities;

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Regression algorithm demonstrating the option universe filter by greeks and other options data feature
    /// </summary>
    public class OptionUniverseFilterGreeksRegressionAlgorithm : QCAlgorithm, IRegressionAlgorithmDefinition
    {
        private const string UnderlyingTicker = "GOOG";
        private Symbol _optionSymbol;
        private bool _optionChainReceived;

        protected decimal MinDelta { get; set; }
        protected decimal MaxDelta { get; set; }
        protected decimal MinGamma { get; set; }
        protected decimal MaxGamma { get; set; }
        protected decimal MinVega { get; set; }
        protected decimal MaxVega { get; set; }
        protected decimal MinTheta { get; set; }
        protected decimal MaxTheta { get; set; }
        protected decimal MinRho { get; set; }
        protected decimal MaxRho { get; set; }
        protected decimal MinIv { get; set; }
        protected decimal MaxIv { get; set; }
        protected long MinOpenInterest { get; set; }
        protected long MaxOpenInterest { get; set; }

        public override void Initialize()
        {
            SetStartDate(2015, 12, 24);
            SetEndDate(2015, 12, 24);
            SetCash(100000);

            AddEquity(UnderlyingTicker);
            var option = AddOption(UnderlyingTicker);
            _optionSymbol = option.Symbol;

            MinDelta = 0.5m;
            MaxDelta = 1.5m;
            MinGamma = 0.0001m;
            MaxGamma = 0.0006m;
            MinVega = 0.01m;
            MaxVega = 1.5m;
            MinTheta = -2.0m;
            MaxTheta = -0.5m;
            MinRho = 0.5m;
            MaxRho = 3.0m;
            MinIv = 1.0m;
            MaxIv = 3.0m;
            MinOpenInterest = 100;
            MaxOpenInterest = 500;

            option.SetFilter(u =>
            {
                var totalContracts = u.Count();

                var filteredUniverse = OptionFilter(u);
                var filteredContracts = filteredUniverse.Count();

                if (filteredContracts == totalContracts)
                {
                    throw new RegressionTestException($"Expected filtered universe to have less contracts than original universe." +
                        $"Filtered contracts count ({filteredContracts}) is equal to total contracts count ({totalContracts})");
                }

                return filteredUniverse;
            });
        }

        protected virtual OptionFilterUniverse OptionFilter(OptionFilterUniverse universe)
        {
            // Contracts can be filtered by greeks, implied volatility, open interest:
            return universe
                .Delta(MinDelta, MaxDelta)
                .Gamma(MinGamma, MaxGamma)
                .Vega(MinVega, MaxVega)
                .Theta(MinTheta, MaxTheta)
                .Rho(MinRho, MaxRho)
                .ImpliedVolatility(MinIv, MaxIv)
                .OpenInterest(MinOpenInterest, MaxOpenInterest);

            // Note: there are also shortcuts for these filter methods:
            /*
            return u => universe
                .D(MinDelta, MaxDelta)
                .G(MinGamma, MaxGamma)
                .V(MinVega, MaxVega)
                .T(MinTheta, MaxTheta)
                .R(MinRho, MaxRho)
                .IV(MinIv, MaxIv)
                .OI(MinOpenInterest, MaxOpenInterest);
            */
        }

        public override void OnData(Slice slice)
        {
            if (slice.OptionChains.TryGetValue(_optionSymbol, out var chain) && chain.Contracts.Count > 0)
            {
                Log($"[{Time}] :: Received option chain with {chain.Contracts.Count} contracts");
                _optionChainReceived = true;
            }
        }

        public override void OnEndOfAlgorithm()
        {
            if (!_optionChainReceived)
            {
                throw new RegressionTestException("Option chain was not received.");
            }
        }

        /// <summary>
        /// This is used by the regression test system to indicate if the open source Lean repository has the required data to run this algorithm.
        /// </summary>
        public bool CanRunLocally { get; } = true;

        /// <summary>
        /// This is used by the regression test system to indicate which languages this algorithm is written in.
        /// </summary>
        public virtual List<Language> Languages { get; } = new() { Language.CSharp, Language.Python };

        /// <summary>
        /// Data Points count of all timeslices of algorithm
        /// </summary>
        public long DataPoints => 7113;

        /// <summary>
        /// Data Points count of the algorithm history
        /// </summary>
        public int AlgorithmHistoryDataPoints => 0;

        /// <summary>
        /// Final status of the algorithm
        /// </summary>
        public AlgorithmStatus AlgorithmStatus => AlgorithmStatus.Completed;

        /// <summary>
        /// This is used by the regression test system to indicate what the expected statistics are from running the algorithm
        /// </summary>
        public Dictionary<string, string> ExpectedStatistics => new Dictionary<string, string>
        {
            {"Total Orders", "0"},
            {"Average Win", "0%"},
            {"Average Loss", "0%"},
            {"Compounding Annual Return", "0%"},
            {"Drawdown", "0%"},
            {"Expectancy", "0"},
            {"Start Equity", "100000"},
            {"End Equity", "100000"},
            {"Net Profit", "0%"},
            {"Sharpe Ratio", "0"},
            {"Sortino Ratio", "0"},
            {"Probabilistic Sharpe Ratio", "0%"},
            {"Loss Rate", "0%"},
            {"Win Rate", "0%"},
            {"Profit-Loss Ratio", "0"},
            {"Alpha", "0"},
            {"Beta", "0"},
            {"Annual Standard Deviation", "0"},
            {"Annual Variance", "0"},
            {"Information Ratio", "0"},
            {"Tracking Error", "0"},
            {"Treynor Ratio", "0"},
            {"Total Fees", "$0.00"},
            {"Estimated Strategy Capacity", "$0"},
            {"Lowest Capacity Asset", ""},
            {"Portfolio Turnover", "0%"},
            {"Drawdown Recovery", "0"},
            {"OrderListHash", "d41d8cd98f00b204e9800998ecf8427e"}
        };
    }
}
