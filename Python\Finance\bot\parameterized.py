# fmt: off
import os
import sys
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
import utils
# fmt: on

import datetime as dt
import logging
import math
import subprocess
import sys
import threading
import strategies
import time
from concurrent.futures import thread
import pandas as pd
import pandas_ta as ta
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
import inspect
import numpy as np
import pandas as pd
from numba import njit, jit, config
import vectorbtpro as vbt
import warnings
import vbt_custom_simulation
from collections import namedtuple
import pandas as pd
import pendulum
import functools
import psutil
import humanize

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
warnings.filterwarnings("ignore", category=RuntimeWarning)
import utils
from aligo import Aligo


def handle():
    data_context = strategies.get_data(as_namedtuple=True)

    with vbt.Timer() as timer:
        signal_params = {
            "rsi_len": 10,
            "ema_slow_window": 42, # vbt.Param(list(range(5, 60))),
            "ema_fast_window": 24, # vbt.Param(list(range(5, 40)), condition="ema_fast_window < ema_slow_window"),
            "bb_window": 13, # vbt.Param(list(range(2, 30))),
            "bb_std": 1.1, # vbt.Param(np.arange(1, 6, 0.1).tolist()),
            "backcandles": vbt.Param(list(range(2, 30))),
        }

        trading_params = {
            "atr_sl_ratio": 1.6, # vbt.Param(np.arange(1, 5, 0.1).tolist()),
            "atr_tp_ratio": 5.4, # vbt.Param(np.arange(1, 15, 0.1).tolist(), condition="trading_params_atr_tp_ratio / trading_params_atr_sl_ratio >= 3"),
            "n_entries": 6,  # vbt.Param(list(range(1, 20))),
            "num_step_entries": 2,  # vbt.Param(list(range(0, 10))),
            "sl_levels": 5,  # vbt.Param(list(range(2, 20))),
            "tp_levels": 16,  # vbt.Param(list(range(2, 30)), condition="trading_params_tp_levels > trading_params_sl_levels"),
        }
        pf_results = vbt_custom_simulation.trading_pipeline(
            data_context=data_context,
            trading_params=trading_params,
            signal_func=strategies.get_ema_bbands_signals_nb,
            **signal_params,
        )

        ram_summit = humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)
        logging.info(f"ram_summit_01: {ram_summit}")

        pf_results = pf_results[:-1]  # remove the last column order_records
        pf = pd.concat(pf_results, axis=1)
        pf = pf.reset_index()
        new_columns = {
            0: "max_dd_days",
            1: "max_dd_bars",
            2: "max_drawdown(%)",
            3: "total_return(%)",
            4: "win_rate(%)",
            5: "order_cnt",
            6: "benchmark_return(%)",
            7: "sharpe_ratio(%)",
            8: "long_signals_cnt",
            9: "short_signals_cnt",
            10: "total_signal_pct(%)",
            11: "trading_volume(FDUSD)",
        }
        pf.rename(columns=new_columns, inplace=True)

        total_rows = pf.shape[0]
        pf["max_drawdown(%)"] *= 100
        pf["max_drawdown(%)"] = pf["max_drawdown(%)"].round(2)
        pf["total_return(%)"] *= 100
        pf["total_return(%)"] = pf["total_return(%)"].round(2)
        pf["win_rate(%)"] *= 100
        pf["win_rate(%)"] = pf["win_rate(%)"].round(2)
        pf["benchmark_return(%)"] *= 100
        pf["benchmark_return(%)"] = pf["benchmark_return(%)"].round(2)
        pf["sharpe_ratio(%)"] = pf["sharpe_ratio(%)"].round(2)
        pf["total_signal_pct(%)"] *= 100
        pf["total_signal_pct(%)"] = pf["total_signal_pct(%)"].round(2)
        pf = pf.sort_values(by=["win_rate(%)", "max_drawdown(%)", "sharpe_ratio(%)"], ascending=[False, True, False]).reset_index(drop=True)

        ram_summit = humanize.naturalsize(psutil.Process(os.getpid()).memory_info().rss)
        logging.info(f"ram_summit_02: {ram_summit}")

        logging.info(f"pf(rows: {total_rows}):\n{pf.head(100)}")
    logging.info(f"overfitting (rows: {total_rows}) parameterization cost: `{timer.elapsed()}`, average cost for each: {timer.elapsed(readable=False).total_seconds() / pf.shape[0]: .4f}s")

    if 0:
        pf.set_index(["ema_slow_window", "ema_fast_window"], inplace=True)
        series = pf["max_drawdown(%)"]

        # Before creating heatmap, check for duplicates
        if series.index.duplicated().any():
            series = series[~series.index.duplicated(keep="first")]

        fig = series.vbt.heatmap(
            x_level="ema_slow_window",
            y_level="ema_fast_window",
            symmetric=False,
        )
    elif 0:
        pf.set_index(["trading_params_atr_sl_ratio", "trading_params_atr_tp_ratio"], inplace=True)
        series = pf["max_dd_bars"]
        # series = pf["max_drawdown(%)"]

        # Before creating heatmap, check for duplicates
        if series.index.duplicated().any():
            series = series[~series.index.duplicated(keep="first")]

        fig = series.vbt.heatmap(
            x_level="trading_params_atr_sl_ratio",
            y_level="trading_params_atr_tp_ratio",
            symmetric=False,
        )
    # fig.show()


def main():
    if sys.platform == "win32":
        os.system("cls")
    else:
        os.system("clear")

    t0 = pendulum.now()

    utils.init_logging()

    handle()

    logging.info("-" * 60)
    t1 = pendulum.now()
    delta = t1 - t0
    logging.info(f"elapsed time: `{delta.in_words(locale='en')}`")

    print("about to exit~")


if __name__ == "__main__":
    main()
