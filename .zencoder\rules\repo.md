---
description: Repository Information Overview
alwaysApply: true
---

# Repository Information Overview

## Repository Summary
This repository contains a collection of Python-based financial tools and AI experimentation projects. It serves as a testing ground for various financial data analysis, trading strategies, and AI model interactions.

## Repository Structure
- **Python/Finance**: Financial tools and trading algorithms
- **xAI**: AI experimentation with different models (Gemini, Grok, Cursor)
- **.config**: Configuration files for development tools
- **.vscode**: VS Code editor configuration

### Main Repository Components
- **Finance Tools**: Python-based financial analysis and trading tools
- **AI Experimentation**: Testing different AI models and capabilities
- **Configuration**: Development environment settings

## Projects

### Finance Tools (Python/Finance)
**Configuration File**: Various Python modules

#### Language & Runtime
**Language**: Python
**Version**: 3.12.6
**Package Manager**: pip (implied)

#### Dependencies
**Main Dependencies**:
- alpaca-py: Trading API client
- pandas: Data analysis
- pendulum: Date/time handling
- numba: Performance optimization
- vectorbtpro: Backtesting (optional)
- finnhub: Financial data API
- aligo: Cloud storage integration

#### Build & Installation
```bash
# No explicit build process found
# Dependencies would be installed via:
pip install alpaca-py pandas pendulum numba finnhub-python
```

#### Main Files & Resources
**Entry Points**:
- `Python/Finance/main.py`: Main application entry point
- `Python/Finance/alpaca_module.py`: Alpaca trading integration
- `Python/Finance/tiger_module.py`: Tiger broker integration
- `Python/Finance/utils.py`: Utility functions

**Configuration**:
- `Python/Finance/config/`: Contains API tokens and configuration files

#### Testing
**Framework**: No formal testing framework identified
**Test Files**: Various test files like `test_binance.py`, `test_binance_websocket.py`

### AI Experimentation (xAI)
**Configuration File**: Various Python scripts and notebooks

#### Language & Runtime
**Language**: Python
**Version**: 3.12.6
**Package Manager**: pip (implied)

#### Dependencies
**Main Dependencies**:
- google-genai: Google Generative AI API
- jupyter: Notebook environment
- PIL: Image processing

#### Main Files & Resources
**Entry Points**:
- `xAI/Gemini/xAi.py`: Gemini AI integration
- `xAI/Gemini/ai_testbed.ipynb`: Jupyter notebook for AI testing
- `xAI/Grok/castle.ipynb`: Grok AI experimentation

#### Usage & Operations
```bash
# Run Gemini AI script
python xAI/Gemini/xAi.py

# Open Jupyter notebooks
jupyter notebook xAI/Gemini/ai_testbed.ipynb
```

### Cursor AI Configuration
**Type**: Configuration files for Cursor AI editor

#### Key Resources
**Main Files**:
- `xAI/Cursor/CursorEditorGlobalSettings.txt`: Configuration for Cursor AI editor
- `xAI/Cursor/avoid_freezing.txt`: Guidelines for Cursor AI usage
- `xAI/Cursor/.cursorrules`: Custom rules for Cursor AI

#### Usage & Operations
These files provide configuration and guidelines for using the Cursor AI-powered code editor, focusing on exploration, reasoning depth, and structured thinking processes.