root@iZ6webirnqnan7rl4o725eZ:~# ab -n 100 -c 10 "https://api.binance.com/api/v3/ticker/price?symbol=BNBBTC"
This is ApacheBench, Version 2.3 <$Revision: 1903618 $>
Copyright 1996 Adam <PERSON>wi<PERSON>, Zeus Technology Ltd, http://www.zeustech.net/
Licensed to The Apache Software Foundation, http://www.apache.org/

Benchmarking api.binance.com (be patient).....done


Server Software:        nginx
Server Hostname:        api.binance.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.3,TLS_AES_128_GCM_SHA256,2048,128
Server Temp Key:        X25519 253 bits
TLS Server Name:        api.binance.com

Document Path:          /api/v3/ticker/price?symbol=BNBBTC
Document Length:        40 bytes

Concurrency Level:      10
Time taken for tests:   0.209 seconds
Complete requests:      100
Failed requests:        0
Total transferred:      94194 bytes
HTML transferred:       4000 bytes
Requests per second:    477.53 [#/sec] (mean)
Time per request:       20.941 [ms] (mean)
Time per request:       2.094 [ms] (mean, across all concurrent requests)
Transfer rate:          439.26 [Kbytes/sec] received

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        5    8   1.6      8      16
Processing:     7   10   1.6     10      17
Waiting:        7   10   1.6     10      16
Total:         13   18   2.7     18      29

Percentage of the requests served within a certain time (ms)
  50%     18
  66%     19
  75%     20
  80%     20
  90%     21
  95%     23
  98%     26
  99%     29
 100%     29 (longest request)