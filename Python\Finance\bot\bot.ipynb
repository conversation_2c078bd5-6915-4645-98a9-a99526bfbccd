{"cells": [{"cell_type": "code", "execution_count": 1, "id": "33303e37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Gmt time</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>30.09.2019 00:00:00.000</td>\n", "      <td>1.09425</td>\n", "      <td>1.09426</td>\n", "      <td>1.09405</td>\n", "      <td>1.09406</td>\n", "      <td>585.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30.09.2019 00:05:00.000</td>\n", "      <td>1.09408</td>\n", "      <td>1.09414</td>\n", "      <td>1.09401</td>\n", "      <td>1.09409</td>\n", "      <td>289.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>30.09.2019 00:10:00.000</td>\n", "      <td>1.09410</td>\n", "      <td>1.09423</td>\n", "      <td>1.09408</td>\n", "      <td>1.09410</td>\n", "      <td>276.24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30.09.2019 00:15:00.000</td>\n", "      <td>1.09409</td>\n", "      <td>1.09410</td>\n", "      <td>1.09388</td>\n", "      <td>1.09389</td>\n", "      <td>439.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30.09.2019 00:20:00.000</td>\n", "      <td>1.09390</td>\n", "      <td>1.09395</td>\n", "      <td>1.09388</td>\n", "      <td>1.09395</td>\n", "      <td>341.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225081</th>\n", "      <td>30.09.2022 20:35:00.000</td>\n", "      <td>0.98028</td>\n", "      <td>0.98034</td>\n", "      <td>0.98001</td>\n", "      <td>0.98022</td>\n", "      <td>624.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225082</th>\n", "      <td>30.09.2022 20:40:00.000</td>\n", "      <td>0.98023</td>\n", "      <td>0.98047</td>\n", "      <td>0.98007</td>\n", "      <td>0.98030</td>\n", "      <td>408.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225083</th>\n", "      <td>30.09.2022 20:45:00.000</td>\n", "      <td>0.98026</td>\n", "      <td>0.98034</td>\n", "      <td>0.98019</td>\n", "      <td>0.98031</td>\n", "      <td>317.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225084</th>\n", "      <td>30.09.2022 20:50:00.000</td>\n", "      <td>0.98031</td>\n", "      <td>0.98067</td>\n", "      <td>0.97987</td>\n", "      <td>0.97999</td>\n", "      <td>1472.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225085</th>\n", "      <td>30.09.2022 20:55:00.000</td>\n", "      <td>0.98000</td>\n", "      <td>0.98066</td>\n", "      <td>0.97993</td>\n", "      <td>0.98026</td>\n", "      <td>1068.81</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>225086 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                       Gmt time     Open     High      Low    Close   Volume\n", "0       30.09.2019 00:00:00.000  1.09425  1.09426  1.09405  1.09406   585.10\n", "1       30.09.2019 00:05:00.000  1.09408  1.09414  1.09401  1.09409   289.39\n", "2       30.09.2019 00:10:00.000  1.09410  1.09423  1.09408  1.09410   276.24\n", "3       30.09.2019 00:15:00.000  1.09409  1.09410  1.09388  1.09389   439.29\n", "4       30.09.2019 00:20:00.000  1.09390  1.09395  1.09388  1.09395   341.23\n", "...                         ...      ...      ...      ...      ...      ...\n", "225081  30.09.2022 20:35:00.000  0.98028  0.98034  0.98001  0.98022   624.12\n", "225082  30.09.2022 20:40:00.000  0.98023  0.98047  0.98007  0.98030   408.20\n", "225083  30.09.2022 20:45:00.000  0.98026  0.98034  0.98019  0.98031   317.29\n", "225084  30.09.2022 20:50:00.000  0.98031  0.98067  0.97987  0.97999  1472.13\n", "225085  30.09.2022 20:55:00.000  0.98000  0.98066  0.97993  0.98026  1068.81\n", "\n", "[225086 rows x 6 columns]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df=pd.read_csv(\"EURUSD_Candlestick_5_M_ASK_30.09.2019-30.09.2022.csv\")\n", "df"]}, {"cell_type": "code", "execution_count": 2, "id": "c3f880ce", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9520\\286423569.py:1: FutureWarning: The default value of regex will change from True to False in a future version.\n", "  df[\"Gmt time\"]=df[\"Gmt time\"].str.replace(\".000\",\"\")\n"]}, {"data": {"text/plain": ["224989"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"Gmt time\"]=df[\"Gmt time\"].str.replace(\".000\",\"\")\n", "df['Gmt time']=pd.to_datetime(df['Gmt time'],format='%d.%m.%Y %H:%M:%S')\n", "df.set_index(\"Gmt time\", inplace=True)\n", "df=df[df.High!=df.Low]\n", "len(df)"]}, {"cell_type": "code", "execution_count": 3, "id": "d3eb031c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9520\\3882960619.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df[\"VWAP\"]=ta.vwap(df.High, df.Low, df.Close, df.Volume)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9520\\3882960619.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df['RSI']=ta.rsi(df.Close, length=16)\n"]}], "source": ["import pandas_ta as ta\n", "df[\"VWAP\"]=ta.vwap(df.High, df.Low, df.Close, df.Volume)\n", "df['RSI']=ta.rsi(df.Close, length=16)\n", "my_bbands = ta.bbands(df.Close, length=14, std=2.0)\n", "df=df.join(my_bbands)"]}, {"cell_type": "code", "execution_count": 4, "id": "6d2cc33a", "metadata": {}, "outputs": [], "source": ["VWAPsignal = [0]*len(df)\n", "backcandles = 15\n", "\n", "for row in range(backcandles, len(df)):\n", "    upt = 1\n", "    dnt = 1\n", "    for i in range(row-backcandles, row+1):\n", "        if max(df.Open[i], df.Close[i])>=df.VWAP[i]:\n", "            dnt=0\n", "        if min(df.Open[i], df.Close[i])<=df.VWAP[i]:\n", "            upt=0\n", "    if upt==1 and dnt==1:\n", "        VWAPsignal[row]=3\n", "    elif upt==1:\n", "        VWAPsignal[row]=2\n", "    elif dnt==1:\n", "        VWAPsignal[row]=1\n", "\n", "df['VWAPSignal'] = VWAPsignal"]}, {"cell_type": "code", "execution_count": 5, "id": "9a9981d6", "metadata": {}, "outputs": [], "source": ["def TotalSignal(l):\n", "    if (df.VWAPSignal[l]==2\n", "        and df.Close[l]<=df['BBL_14_2.0'][l]\n", "        and df.RSI[l]<45):\n", "            return 2\n", "    if (df.VWAPSignal[l]==1\n", "        and df.Close[l]>=df['BBU_14_2.0'][l]\n", "        and df.RSI[l]>55):\n", "            return 1\n", "    return 0\n", "        \n", "TotSignal = [0]*len(df)\n", "for row in range(backcandles, len(df)): #careful backcandles used previous cell\n", "    TotSignal[row] = TotalSignal(row)\n", "df['TotalSignal'] = TotSignal"]}, {"cell_type": "code", "execution_count": 6, "id": "65435b94", "metadata": {}, "outputs": [{"data": {"text/plain": ["Open           2781\n", "High           2781\n", "Low            2781\n", "Close          2781\n", "Volume         2781\n", "VWAP           2781\n", "RSI            2781\n", "BBL_14_2.0     2781\n", "BBM_14_2.0     2781\n", "BBU_14_2.0     2781\n", "BBB_14_2.0     2781\n", "BBP_14_2.0     2781\n", "VWAPSignal     2781\n", "TotalSignal    2781\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df.TotalSignal!=0].count()"]}, {"cell_type": "code", "execution_count": 7, "id": "e7080a9a", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "def pointposbreak(x):\n", "    if x['TotalSignal']==1:\n", "        return x['High']+1e-4\n", "    elif x['TotalSignal']==2:\n", "        return x['Low']-1e-4\n", "    else:\n", "        return np.nan\n", "\n", "df['pointposbreak'] = df.apply(lambda row: pointposbreak(row), axis=1)"]}, {"cell_type": "code", "execution_count": 8, "id": "0d0e2a76", "metadata": {"scrolled": false}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"close": [1.10751, 1.10752, 1.10746, 1.10752, 1.10748, 1.10751, 1.10756, 1.10754, 1.10751, 1.10753, 1.10752, 1.10752, 1.10751, 1.10752, 1.10753, 1.10752, 1.1075, 1.10747, 1.10746, 1.1074, 1.10742, 1.10739, 1.10742, 1.10757, 1.10767, 1.10775, 1.10755, 1.10775, 1.10803, 1.10796, 1.10796, 1.10802, 1.10802, 1.10791, 1.10788, 1.10776, 1.10782, 1.10787, 1.10789, 1.10807, 1.10792, 1.10808, 1.10778, 1.10776, 1.1078, 1.10776, 1.1075, 1.10756, 1.10746, 1.10728, 1.10682, 1.1069, 1.10677, 1.10704, 1.10709, 1.10714, 1.10701, 1.10687, 1.10671, 1.10694, 1.10709, 1.10714, 1.10705, 1.1068, 1.10673, 1.10691, 1.10681, 1.10673, 1.10673, 1.10656, 1.10669, 1.1066, 1.10673, 1.10693, 1.10685, 1.10676, 1.10661, 1.10683, 1.10698, 1.10699, 1.10686, 1.10672, 1.10686, 1.10708, 1.10699, 1.10697, 1.10693, 1.10722, 1.10735, 1.10732, 1.10721, 1.10717, 1.10741, 1.10727, 1.10721, 1.10741, 1.10739, 1.10746, 1.10776, 1.10787, 1.10792, 1.10781, 1.10784, 1.10769, 1.10765, 1.10761, 1.10767, 1.10772, 1.10763, 1.10756, 1.10748, 1.10758, 1.10767, 1.10774, 1.10774, 1.10796, 1.108, 1.10806, 1.10801, 1.10799, 1.10783, 1.10783, 1.10777, 1.10769, 1.10793, 1.10824, 1.10821, 1.10788, 1.10777, 1.10786, 1.10811, 1.1078, 1.10777, 1.10734, 1.10736, 1.10734, 1.10733, 1.10735, 1.10734, 1.10747, 1.10756, 1.10777, 1.10787, 1.10806, 1.10831, 1.10804, 1.10806, 1.10775, 1.10796, 1.10801, 1.10794, 1.10793, 1.10811, 1.10811, 1.10792, 1.10817, 1.10796, 1.10796, 1.1074, 1.1077, 1.10781, 1.10792, 1.1081, 1.10806, 1.10817, 1.10816, 1.10816, 1.10817, 1.10808, 1.10802, 1.10806, 1.10796, 1.10798, 1.10802, 1.10816, 1.10801, 1.10788, 1.10786, 1.10798, 1.10802, 1.10817, 1.10826, 1.1083, 1.1083, 1.10814, 1.10806, 1.10785, 1.10769, 1.10776, 1.10787, 1.10777, 1.10776, 1.10778, 1.10778, 1.10787, 1.10795, 1.108, 1.10797, 1.10798, 1.10799, 1.10802, 1.10781, 1.10787, 1.10776, 1.1078, 1.1077, 1.10773, 1.10783, 1.10777, 1.10771, 1.10776, 1.10773, 1.10787, 1.10787, 1.10783, 1.10771, 1.10769, 1.10773, 1.1079, 1.10773, 1.10773, 1.10788, 1.10817, 1.10792, 1.10787, 1.10782, 1.10789, 1.108, 1.10796, 1.10786, 1.10783, 1.1079, 1.10776, 1.10777, 1.10775, 1.10785, 1.10783, 1.10781, 1.10773, 1.10781, 1.10778, 1.10775, 1.10782, 1.10787, 1.1079, 1.10783, 1.10781, 1.10778, 1.10776, 1.10773, 1.10773, 1.10773, 1.10782, 1.10784, 1.10788, 1.10793, 1.10792, 1.10795, 1.10784, 1.10776, 1.10735, 1.10739, 1.10732, 1.10735, 1.10717, 1.10723, 1.10725, 1.1072, 1.10721, 1.10729, 1.10714, 1.10707, 1.10714, 1.10757, 1.10756, 1.10752, 1.10752, 1.10739, 1.10754, 1.10748, 1.10739, 1.10739, 1.10743, 1.10738, 1.10748, 1.10748, 1.10739, 1.10751, 1.10747, 1.10747, 1.10743, 1.10742, 1.10739, 1.10742, 1.10738, 1.1074, 1.10739, 1.10743, 1.10743, 1.10738, 1.10743, 1.10742, 1.10733, 1.10726, 1.10727, 1.10728, 1.10728, 1.10732, 1.10733, 1.10743, 1.10743, 1.10737, 1.10742, 1.10725, 1.10744, 1.10727, 1.10722, 1.10719, 1.10721, 1.10727, 1.10726, 1.10727, 1.10722, 1.10727, 1.10722, 1.10712, 1.10703, 1.10698, 1.1069, 1.10688, 1.10703, 1.10699, 1.10697, 1.10718, 1.10706, 1.10694, 1.1069, 1.10688, 1.10688, 1.10677, 1.10673, 1.10634, 1.10636, 1.10627, 1.10613, 1.10619, 1.10604, 1.10604, 1.10577, 1.10588], "high": [1.10752, 1.10753, 1.10754, 1.10753, 1.10752, 1.10754, 1.10756, 1.10756, 1.10755, 1.10753, 1.10754, 1.10753, 1.10752, 1.10753, 1.10755, 1.10753, 1.10752, 1.1075, 1.1075, 1.10747, 1.10742, 1.10742, 1.10742, 1.10758, 1.10769, 1.10775, 1.10786, 1.10775, 1.10808, 1.10807, 1.10801, 1.10814, 1.10803, 1.10803, 1.10801, 1.10789, 1.10784, 1.10788, 1.1079, 1.10818, 1.10815, 1.10809, 1.10806, 1.10777, 1.1078, 1.1078, 1.1078, 1.10759, 1.10757, 1.1075, 1.10733, 1.1072, 1.10691, 1.10707, 1.10724, 1.10719, 1.10729, 1.10703, 1.10704, 1.10694, 1.10709, 1.10723, 1.10715, 1.1071, 1.10682, 1.10696, 1.10696, 1.10681, 1.1068, 1.10676, 1.10672, 1.10671, 1.10681, 1.10698, 1.10705, 1.10686, 1.10675, 1.10683, 1.10699, 1.10703, 1.10702, 1.10687, 1.10692, 1.10709, 1.10709, 1.10703, 1.10703, 1.10722, 1.10736, 1.10738, 1.10733, 1.10726, 1.10743, 1.10741, 1.10738, 1.10742, 1.1074, 1.10756, 1.10777, 1.1079, 1.10801, 1.10793, 1.10787, 1.10785, 1.1077, 1.10769, 1.10768, 1.10781, 1.10781, 1.10765, 1.10756, 1.10772, 1.1077, 1.10776, 1.10785, 1.10806, 1.10817, 1.10812, 1.10815, 1.10805, 1.108, 1.10789, 1.10784, 1.10777, 1.10798, 1.1083, 1.10825, 1.10822, 1.10797, 1.10793, 1.10819, 1.10811, 1.10788, 1.10782, 1.10754, 1.10755, 1.10747, 1.10748, 1.10741, 1.10747, 1.10771, 1.10782, 1.1079, 1.10811, 1.10841, 1.10831, 1.10814, 1.10809, 1.10802, 1.10809, 1.10808, 1.108, 1.10814, 1.1082, 1.10817, 1.1083, 1.10817, 1.10813, 1.10796, 1.10779, 1.10789, 1.10797, 1.1081, 1.10813, 1.10827, 1.10826, 1.10825, 1.10817, 1.10823, 1.10822, 1.10815, 1.10817, 1.10801, 1.10806, 1.10817, 1.10819, 1.10802, 1.10796, 1.10801, 1.10803, 1.10817, 1.10829, 1.10831, 1.10831, 1.10831, 1.10816, 1.10808, 1.10789, 1.10779, 1.10787, 1.10786, 1.1078, 1.1078, 1.1079, 1.10788, 1.10796, 1.108, 1.10801, 1.10799, 1.108, 1.10807, 1.10802, 1.10787, 1.10788, 1.10783, 1.10783, 1.10785, 1.10789, 1.10784, 1.10779, 1.10776, 1.10779, 1.10788, 1.10792, 1.10787, 1.10785, 1.1077, 1.10773, 1.1079, 1.10791, 1.10781, 1.10823, 1.10817, 1.10817, 1.10793, 1.10788, 1.10792, 1.108, 1.10809, 1.10798, 1.10788, 1.10792, 1.10794, 1.1078, 1.10787, 1.10787, 1.10786, 1.10788, 1.10782, 1.10781, 1.10781, 1.10778, 1.10784, 1.10788, 1.10797, 1.10791, 1.10791, 1.10784, 1.1078, 1.10779, 1.10778, 1.10778, 1.10783, 1.10793, 1.10794, 1.10794, 1.10798, 1.10795, 1.10796, 1.10784, 1.10779, 1.10744, 1.10741, 1.10738, 1.10736, 1.10723, 1.10725, 1.10726, 1.10725, 1.10729, 1.1073, 1.10719, 1.10716, 1.10764, 1.10774, 1.10758, 1.10758, 1.10753, 1.10756, 1.10754, 1.10748, 1.10742, 1.10743, 1.10743, 1.10749, 1.10754, 1.1075, 1.10751, 1.10751, 1.10748, 1.10748, 1.10743, 1.10743, 1.10745, 1.10742, 1.10741, 1.10739, 1.10744, 1.10744, 1.10744, 1.10743, 1.10743, 1.10743, 1.10732, 1.10728, 1.10728, 1.10728, 1.10734, 1.10735, 1.10744, 1.10754, 1.10744, 1.10743, 1.10743, 1.10744, 1.10744, 1.10728, 1.10729, 1.10722, 1.10728, 1.10729, 1.10728, 1.10729, 1.10728, 1.10728, 1.10723, 1.10714, 1.10714, 1.10697, 1.10694, 1.10703, 1.10708, 1.10702, 1.10719, 1.10727, 1.10705, 1.10703, 1.10691, 1.1069, 1.10689, 1.10682, 1.10676, 1.10649, 1.10649, 1.10627, 1.10621, 1.10629, 1.10615, 1.10611, 1.10589], "low": [1.10745, 1.1075, 1.10744, 1.10743, 1.10745, 1.10746, 1.1075, 1.10754, 1.10751, 1.10749, 1.1075, 1.1075, 1.1075, 1.10746, 1.1075, 1.1075, 1.10747, 1.10746, 1.10746, 1.10739, 1.10738, 1.10735, 1.10739, 1.10741, 1.10755, 1.10767, 1.10754, 1.10754, 1.10775, 1.10794, 1.10782, 1.10793, 1.10786, 1.10787, 1.10784, 1.10776, 1.10773, 1.10777, 1.10785, 1.10788, 1.1079, 1.10787, 1.10773, 1.10769, 1.10767, 1.10775, 1.10749, 1.10749, 1.10737, 1.10721, 1.1068, 1.10679, 1.1066, 1.10674, 1.10701, 1.10702, 1.10694, 1.10676, 1.10666, 1.10667, 1.10688, 1.10704, 1.10697, 1.10678, 1.10664, 1.10673, 1.10671, 1.10665, 1.10652, 1.10646, 1.10653, 1.10651, 1.10657, 1.10672, 1.10683, 1.10665, 1.10658, 1.10661, 1.10684, 1.10692, 1.10675, 1.10666, 1.10663, 1.10686, 1.10692, 1.10693, 1.10693, 1.10688, 1.10718, 1.10728, 1.10712, 1.10713, 1.10712, 1.10722, 1.10721, 1.10718, 1.10732, 1.1074, 1.10745, 1.1076, 1.10784, 1.1078, 1.10774, 1.10768, 1.1076, 1.10748, 1.10757, 1.10768, 1.10762, 1.10747, 1.10735, 1.10749, 1.10756, 1.10757, 1.1076, 1.10773, 1.1079, 1.10783, 1.10793, 1.10793, 1.10768, 1.10775, 1.10766, 1.10757, 1.10764, 1.10785, 1.1081, 1.10786, 1.10773, 1.10763, 1.10786, 1.10774, 1.10773, 1.10733, 1.10721, 1.10732, 1.10727, 1.10726, 1.10724, 1.10732, 1.10744, 1.10755, 1.10775, 1.10783, 1.10803, 1.10799, 1.10789, 1.10772, 1.10776, 1.10792, 1.10788, 1.10788, 1.10793, 1.10805, 1.10785, 1.10792, 1.10793, 1.10788, 1.1074, 1.10739, 1.10768, 1.10777, 1.10787, 1.108, 1.10807, 1.10814, 1.10809, 1.10802, 1.10808, 1.10802, 1.10798, 1.10789, 1.1079, 1.10793, 1.10799, 1.10791, 1.10785, 1.10783, 1.10787, 1.10792, 1.10797, 1.10813, 1.10825, 1.10826, 1.10812, 1.10801, 1.10785, 1.10763, 1.10769, 1.10774, 1.10776, 1.10775, 1.10767, 1.10777, 1.10775, 1.10778, 1.10789, 1.10793, 1.10792, 1.10787, 1.10795, 1.1078, 1.10776, 1.10776, 1.10775, 1.10765, 1.10762, 1.10773, 1.10776, 1.10769, 1.10761, 1.10764, 1.10773, 1.10785, 1.10782, 1.10769, 1.10764, 1.10766, 1.10772, 1.10773, 1.10772, 1.10773, 1.10772, 1.10791, 1.10787, 1.10778, 1.1078, 1.10782, 1.10788, 1.10783, 1.10781, 1.10778, 1.10775, 1.10775, 1.10773, 1.10776, 1.10781, 1.1078, 1.10771, 1.10772, 1.10778, 1.10775, 1.10773, 1.10776, 1.10783, 1.10774, 1.10779, 1.10776, 1.10769, 1.10768, 1.10772, 1.10771, 1.10772, 1.10782, 1.1078, 1.10784, 1.1079, 1.10788, 1.10781, 1.1077, 1.10727, 1.10727, 1.10717, 1.10726, 1.10713, 1.10706, 1.10708, 1.10715, 1.10716, 1.10721, 1.10708, 1.10702, 1.10704, 1.10715, 1.10755, 1.10747, 1.10752, 1.10737, 1.10739, 1.10747, 1.10738, 1.10734, 1.10731, 1.10729, 1.10737, 1.10748, 1.10737, 1.10738, 1.10741, 1.10743, 1.10737, 1.10737, 1.10739, 1.10737, 1.10731, 1.10737, 1.10738, 1.10738, 1.10737, 1.10738, 1.10738, 1.10742, 1.10732, 1.10726, 1.10721, 1.10726, 1.10722, 1.10727, 1.10729, 1.10733, 1.10742, 1.10736, 1.10737, 1.10725, 1.10724, 1.10724, 1.10719, 1.10718, 1.10714, 1.10722, 1.10722, 1.1072, 1.10722, 1.10721, 1.10722, 1.10712, 1.10701, 1.10687, 1.10686, 1.10682, 1.10686, 1.10697, 1.10688, 1.10692, 1.10705, 1.10662, 1.10684, 1.10678, 1.10673, 1.10658, 1.10666, 1.10608, 1.10627, 1.10627, 1.10597, 1.10608, 1.10598, 1.10589, 1.10571, 1.1056], "open": [1.10745, 1.10751, 1.10753, 1.10745, 1.10751, 1.10748, 1.1075, 1.10755, 1.10755, 1.10752, 1.10753, 1.10751, 1.10751, 1.10751, 1.10751, 1.10752, 1.10751, 1.10749, 1.10747, 1.10747, 1.10739, 1.10741, 1.10739, 1.10742, 1.10756, 1.10767, 1.10775, 1.10755, 1.10775, 1.10803, 1.10797, 1.10796, 1.10802, 1.10803, 1.10791, 1.10787, 1.10775, 1.10783, 1.10788, 1.10788, 1.10805, 1.10792, 1.10805, 1.10777, 1.10777, 1.10778, 1.10775, 1.1075, 1.10756, 1.10745, 1.10728, 1.10683, 1.10691, 1.10677, 1.10704, 1.10709, 1.10717, 1.10702, 1.10687, 1.1067, 1.10694, 1.10709, 1.10714, 1.10705, 1.10681, 1.10673, 1.10691, 1.10681, 1.10673, 1.10672, 1.10657, 1.10668, 1.1066, 1.10673, 1.10692, 1.10686, 1.10674, 1.10661, 1.10684, 1.10697, 1.10698, 1.10687, 1.10672, 1.10687, 1.10709, 1.10698, 1.10697, 1.10693, 1.10721, 1.10735, 1.10732, 1.10722, 1.10717, 1.10741, 1.10726, 1.10721, 1.1074, 1.1074, 1.10746, 1.10775, 1.10787, 1.10793, 1.10782, 1.10784, 1.1077, 1.10764, 1.10761, 1.10768, 1.10771, 1.10764, 1.10755, 1.10749, 1.10757, 1.10767, 1.10772, 1.10773, 1.10795, 1.108, 1.10805, 1.10802, 1.108, 1.10782, 1.10781, 1.10776, 1.10768, 1.10794, 1.10823, 1.10822, 1.10789, 1.10777, 1.10786, 1.1081, 1.10781, 1.10776, 1.10735, 1.10736, 1.10733, 1.10733, 1.10734, 1.10734, 1.10746, 1.10755, 1.10777, 1.10786, 1.10805, 1.1083, 1.10805, 1.10806, 1.10776, 1.10796, 1.10802, 1.10794, 1.10793, 1.1081, 1.10811, 1.10793, 1.10816, 1.10797, 1.10796, 1.10739, 1.10771, 1.10782, 1.10793, 1.10809, 1.10808, 1.10818, 1.10815, 1.10816, 1.10816, 1.10808, 1.10803, 1.10806, 1.10795, 1.10797, 1.10801, 1.10817, 1.108, 1.10788, 1.10787, 1.10799, 1.10802, 1.10817, 1.10827, 1.1083, 1.1083, 1.10814, 1.10807, 1.10785, 1.10769, 1.10777, 1.10786, 1.10777, 1.10775, 1.10777, 1.10778, 1.10785, 1.10797, 1.108, 1.10798, 1.10797, 1.10798, 1.10801, 1.10781, 1.10786, 1.10775, 1.1078, 1.10769, 1.10773, 1.10781, 1.10776, 1.10771, 1.10775, 1.10773, 1.10787, 1.10786, 1.10784, 1.1077, 1.10769, 1.10772, 1.1079, 1.10779, 1.10773, 1.10791, 1.10817, 1.10791, 1.10788, 1.10781, 1.10789, 1.108, 1.10797, 1.10785, 1.10785, 1.10791, 1.10776, 1.10777, 1.10776, 1.10784, 1.10783, 1.10782, 1.10773, 1.10781, 1.10778, 1.10776, 1.10783, 1.10788, 1.10791, 1.10782, 1.10781, 1.10777, 1.10775, 1.10772, 1.10773, 1.10772, 1.10782, 1.10785, 1.10788, 1.10793, 1.10792, 1.10796, 1.10783, 1.10777, 1.10735, 1.10739, 1.10732, 1.10736, 1.10718, 1.10723, 1.10725, 1.10721, 1.10721, 1.10728, 1.10713, 1.10708, 1.10715, 1.10759, 1.10757, 1.10752, 1.10751, 1.10739, 1.10753, 1.10748, 1.10737, 1.10739, 1.10742, 1.10737, 1.10748, 1.10749, 1.10738, 1.10751, 1.10747, 1.10746, 1.10742, 1.10741, 1.10738, 1.10742, 1.10738, 1.10739, 1.10739, 1.10743, 1.10743, 1.10738, 1.10743, 1.10743, 1.10732, 1.10726, 1.10727, 1.10727, 1.10727, 1.10733, 1.10733, 1.10744, 1.10743, 1.10737, 1.10741, 1.10724, 1.10744, 1.10728, 1.10721, 1.1072, 1.10722, 1.10728, 1.10726, 1.10728, 1.10723, 1.10727, 1.10723, 1.10713, 1.10704, 1.10696, 1.10691, 1.10688, 1.10702, 1.107, 1.10697, 1.10719, 1.10705, 1.10694, 1.10691, 1.10684, 1.10689, 1.10678, 1.1067, 1.10635, 1.10635, 1.10627, 1.10612, 1.1062, 1.10604, 1.10603, 1.10577], "type": "candlestick", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349]}, {"line": {"color": "blue", "width": 1}, "name": "VWAP", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349], "y": [1.107034327722834, 1.107041544820879, 1.1070537549764852, 1.107066359530671, 1.1070731199635535, 1.1070808296527017, 1.1070852199778458, 1.1070872955522064, 1.1070939645512965, 1.10709671840159, 1.107101304786267, 1.1071032346933494, 1.107104934440374, 1.1071080104417033, 1.1071106520126628, 1.1071143091905018, 1.107117056746152, 1.1071196337809186, 1.107120750383367, 1.1071219049374164, 1.1071252329521042, 1.1071295876586882, 1.1071318019895657, 1.1071372197867257, 1.1071449559406303, 1.1071505124420715, 1.1071621736148856, 1.107170625277647, 1.1071885296403132, 1.1072147743543996, 1.1072322858376793, 1.1072678352765246, 1.1072833608264312, 1.107292523947638, 1.1072976851411847, 1.1073005774483664, 1.1073058504625737, 1.107311141843533, 1.1073176731565013, 1.107342195043072, 1.1073528014852372, 1.1073688877001648, 1.1073787207031605, 1.107383127537483, 1.1073893269710648, 1.107395699428882, 1.1073987989007579, 1.1074002004120231, 1.1074015915961113, 1.1073998733991275, 1.1073762366057098, 1.1073493614577714, 1.1073255657174605, 1.1073182037665987, 1.1073122702411624, 1.1073076518875957, 1.107302561525286, 1.1072912849608207, 1.107280819984635, 1.107270369687784, 1.1072673468357628, 1.1072647419879962, 1.1072606738826913, 1.10725345054896, 1.1072461508712534, 1.1072407290938788, 1.1072350196759764, 1.1072285890172486, 1.1072199299569792, 1.107207281453601, 1.1072000464657903, 1.1071941636320979, 1.1071881755729631, 1.107183657526729, 1.1071773378626633, 1.1071696614532194, 1.1071621395620284, 1.1071570047906356, 1.1071557027533878, 1.107153493948202, 1.1071505968095905, 1.1071451771876109, 1.107140742586288, 1.107139258052085, 1.107137874866665, 1.1071362189539735, 1.107134845990275, 1.107134562399073, 1.1071360382033328, 1.1071383834011177, 1.1071391922436447, 1.1071397035221964, 1.1071419642483993, 1.1071433828799293, 1.1071444994688253, 1.1071461463527168, 1.1071476901798318, 1.1071508377837973, 1.107157089999217, 1.1071662167397067, 1.1071751365751203, 1.1071789381436314, 1.1071821132897814, 1.107186043969828, 1.1071885317579926, 1.1071918383367767, 1.107195753495989, 1.1071985750003142, 1.10720151110075, 1.1072047341964442, 1.1072080956211088, 1.1072115527693882, 1.1072151079686874, 1.1072190247710867, 1.1072255061350846, 1.107234982254082, 1.107243652356075, 1.1072533538395701, 1.1072621988479525, 1.1072712700877976, 1.1072763867215172, 1.1072806756431883, 1.1072853256439006, 1.1072892231783111, 1.1072925838805148, 1.1073007522728193, 1.1073074786342751, 1.1073134636231514, 1.1073194431343207, 1.1073252772507627, 1.1073347959113176, 1.107340147251842, 1.1073440399119616, 1.1073460684638734, 1.1073464041622465, 1.1073470504300478, 1.107347128097307, 1.1073472779090754, 1.1073470934073133, 1.107347801790441, 1.1073496652529484, 1.1073527827187217, 1.107356504612514, 1.1073622540354648, 1.107374741382711, 1.1073811342726296, 1.1073886441715455, 1.1073946200032556, 1.1073996460231905, 1.1074045533159207, 1.107407685080887, 1.1074092025819422, 1.1074130208064585, 1.1074169411754704, 1.107421952780813, 1.1074291232292104, 1.1074320494047185, 1.107434548205201, 1.1074354286691888, 1.107436465281373, 1.1074391358125861, 1.1074413049178948, 1.1074439338633997, 1.1074463097901237, 1.1074497724814598, 1.1074541611943574, 1.1074596773024892, 1.107462938878598, 1.1074646923003681, 1.1074662943217617, 1.107469755844095, 1.1074726851271495, 1.1074741054491606, 1.1074767850290115, 1.1074791089671527, 1.1074803717071031, 1.1074816749702119, 1.1074824782913557, 1.1074843967442274, 1.1074863707095974, 1.1074877890181907, 1.1074903155272133, 1.1074920215062696, 1.107494350069157, 1.1074953599713127, 1.1074964494787543, 1.107497103514542, 1.107497764461176, 1.107498557282031, 1.10749938452083, 1.1074999277021444, 1.1075002928547124, 1.1075009254443842, 1.1075018441953617, 1.1075032860540286, 1.1075042802310906, 1.1075048227046422, 1.1075057649602476, 1.1075066826928608, 1.1075074841259644, 1.107508302428818, 1.107509185699042, 1.1075102932885825, 1.107511094062346, 1.1075118721007005, 1.107512466156927, 1.1075131904346738, 1.1075138470932206, 1.107514127737106, 1.1075144045381542, 1.1075145796990737, 1.1075147519323592, 1.1075151452426448, 1.1075153169569565, 1.10751549760588, 1.1075156507530326, 1.1075157472454145, 1.107515917931684, 1.107516250073066, 1.1075163561945784, 1.1075164067401204, 1.1075175177551408, 1.1075182145985925, 1.1075188057833831, 1.1075189165479797, 1.1075190408522424, 1.1075191939519988, 1.107519575554626, 1.1075198021816075, 1.1075199600649428, 1.1075201174567322, 1.10752077754187, 1.1075215637982183, 1.1075218494486876, 1.1075224417234268, 1.107522837186775, 1.1075230658756148, 1.1075233534715456, 1.1075236095333696, 1.107523973466171, 1.1075242958179814, 1.1075247486990296, 1.1075251708337883, 1.1078366666666666, 1.1078679423124487, 1.1078566151283396, 1.107851769685821, 1.1078429620621706, 1.1078312616485229, 1.1078147879448919, 1.1078108698472344, 1.1078023355170792, 1.1078010156614346, 1.1078085106143825, 1.1078158979664245, 1.1078214743614399, 1.1078295119471055, 1.107832525280478, 1.107835068683748, 1.107824413500585, 1.1077728145145773, 1.107734477946597, 1.1077176297752354, 1.1076990561937938, 1.1076746025126172, 1.107658823565412, 1.1076211895044594, 1.1076042501352854, 1.1075945775287097, 1.10758377581726, 1.1075721453072502, 1.1075521239908939, 1.1075410905577119, 1.107536215946321, 1.1075390684729263, 1.1075385367063917, 1.1075385870664876, 1.10753620752794, 1.107534909131698, 1.1075338718796206, 1.1075326072839353, 1.107529996223119, 1.1075274135366515, 1.1075257704383583, 1.1075247212783916, 1.107524355255905, 1.107522333510657, 1.1075213562393627, 1.107519836264729, 1.1075191336479628, 1.1075178906838876, 1.1075168075819473, 1.107515837090394, 1.1075149835928348, 1.1075135641614855, 1.1075124981141238, 1.1075110098721503, 1.1075101243101184, 1.1075091636228735, 1.1075086703626273, 1.1075081701617524, 1.1075077804376834, 1.1075054319516575, 1.1075029561108014, 1.1075012168950351, 1.1074998380355168, 1.107498615782205, 1.1074971511006553, 1.1074965172521531, 1.1074959342137594, 1.1074953669022618, 1.1074938570048527, 1.1074934555940144, 1.107490057360922, 1.1074879612490987, 1.107485083504402, 1.1074831236889333, 1.1074807918741063, 1.1074779295365402, 1.1074770015804538, 1.1074747854801341, 1.1074723438455527, 1.1074707827242138, 1.1074690503103408, 1.1074681805041096, 1.1074649747873773, 1.1074604291609724, 1.1074482027720718, 1.107442407410971, 1.1074313133489668, 1.1074246070557996, 1.107420069002137, 1.1074125260104517, 1.1074090219258605, 1.107402270452161, 1.1073713587590088, 1.1073552459783773, 1.1073472229871149, 1.1073345109359407, 1.1073090241418755, 1.107292918136845, 1.1072570507300814, 1.1072278593010343, 1.1072124911496488, 1.1071867831313595, 1.1071577276748004, 1.1071270495744123, 1.1070960539515906, 1.1070462596205228, 1.1070270086520333]}, {"line": {"color": "green", "width": 1}, "name": "BBL", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349], "y": [1.1070405552757991, 1.1070850336865152, 1.1071192440046982, 1.107158303284522, 1.1071852577534997, 1.107250030763235, 1.1073028102982052, 1.1073176876926158, 1.1073490183567478, 1.107389725731464, 1.1074308314046486, 1.1074412546456551, 1.1074534319575984, 1.107469174851682, 1.107470072105546, 1.107470072124706, 1.1074834581288224, 1.1074718226782816, 1.1074648816699748, 1.107429484757955, 1.107413756703515, 1.1073904812548092, 1.107379439185244, 1.1073746579564057, 1.107348748543763, 1.107313404809023, 1.1073151399502634, 1.1072964561731478, 1.1072169930598983, 1.107194664713496, 1.1071905984252925, 1.1071964351249353, 1.1072190581718953, 1.1072771538401078, 1.1073422748930346, 1.107424655218978, 1.1075185300322103, 1.1075755574407549, 1.1076103358286036, 1.1076230515226066, 1.1077278051357446, 1.107759124116521, 1.1077305682093794, 1.1077001022032262, 1.1076834493803926, 1.107665124757504, 1.107566614232076, 1.107509018065787, 1.1074311117293778, 1.1073108134369025, 1.1070314650737552, 1.1068586147553088, 1.1066817930155761, 1.106628848194039, 1.106591368032172, 1.1066096331068642, 1.1065824362995849, 1.1065445431904652, 1.1065048045326706, 1.1065438599436517, 1.1065681040251492, 1.1066249903461811, 1.1066793121359246, 1.1066766631968639, 1.1066543551646901, 1.1066554963535078, 1.1066646733428593, 1.1066257445599692, 1.1065976250548153, 1.1065385620752497, 1.1065153024256236, 1.1064741010135364, 1.1064772057979373, 1.10647743055225, 1.1064931850621054, 1.1065277518789247, 1.1065336882377415, 1.1065330911486386, 1.1065204490287444, 1.1065098011224008, 1.1065100203232388, 1.1065087713029524, 1.1065158182220118, 1.1065432293254671, 1.1065620227198263, 1.106616397320604, 1.1066412639091867, 1.1066060957486836, 1.1065657724123932, 1.1065726645641365, 1.1066557568328377, 1.1066892486539401, 1.1066767553595036, 1.1066882818897283, 1.1067319650522807, 1.1068250302745704, 1.1068927451931354, 1.1069041114991238, 1.1068922969313462, 1.1068986607869273, 1.1069488390204123, 1.1069677232259425, 1.106973148087223, 1.1070019307969592, 1.1070616654200784, 1.1071426237073543, 1.1071715678073184, 1.1072443145395157, 1.1073539639811738, 1.1073918366241025, 1.107421380373049, 1.1074532353997255, 1.1074487930365449, 1.1074579793677581, 1.1074831794074698, 1.1074510430410185, 1.1074201534789172, 1.10738749720735, 1.1073841479166378, 1.1073990341022644, 1.1074138915624456, 1.1074228129294938, 1.1074434296812345, 1.1074720224130061, 1.1075540737307539, 1.1075740434971741, 1.1075979567542658, 1.107623261501399, 1.107630464318498, 1.1076208318185263, 1.10761709575033, 1.107598033887402, 1.1075758040562353, 1.1074238244948496, 1.1073196531223104, 1.1072262695126533, 1.1071467750358852, 1.1070874662623218, 1.1070250921040703, 1.1070358045772664, 1.107079521427611, 1.1070864594182086, 1.107080394313382, 1.107055623779744, 1.1070108937676708, 1.106995955257039, 1.1069828632371725, 1.1070345475041907, 1.107090113779587, 1.107161841414136, 1.1072537442277932, 1.1073574688409498, 1.1074902318518236, 1.1076059315712954, 1.1077076691903165, 1.107749942813276, 1.1077667105766644, 1.107758457787268, 1.1075859264743513, 1.107543667945328, 1.1075289502729466, 1.1075514975992309, 1.107550463168107, 1.1075502366912084, 1.1075480247539153, 1.1075507804022537, 1.1075487259621295, 1.10754631514902, 1.1075557711165425, 1.1075564553599224, 1.1075615622059412, 1.1075615622059412, 1.1077531020073315, 1.1078437314662795, 1.107909774755503, 1.1079347740502092, 1.10788716180027, 1.1078440035578117, 1.1078392144318883, 1.1078416780507503, 1.1078402479749092, 1.1078226290152597, 1.1077976467121546, 1.1077871610824432, 1.1077905641819799, 1.1078046247502977, 1.107774420296649, 1.1076916915994697, 1.1076400752671938, 1.1076222593599034, 1.1075999930883904, 1.1075796688720054, 1.1075507997863212, 1.1075223001340218, 1.107514112310018, 1.1075296156443883, 1.1075652028048715, 1.1076213693318102, 1.107647026326748, 1.107657736574178, 1.1076535292647594, 1.1076828222204063, 1.1077019128666246, 1.1076828222181458, 1.1076891703780165, 1.1076720048992366, 1.1076602164256761, 1.107668551980781, 1.107654195977591, 1.1076272876840965, 1.1076212763619913, 1.10761157160258, 1.1076212091562148, 1.1076364964742034, 1.107672972061591, 1.1076598070756383, 1.1076490829313894, 1.1076453367955115, 1.1076348824059803, 1.107640761715546, 1.107640761715546, 1.1076363644708027, 1.107561375877496, 1.10757646283103, 1.1075858279008504, 1.1075985244794468, 1.1075989459633675, 1.1075944035360998, 1.1075975357043721, 1.1076213758756943, 1.107650427054323, 1.107677800736164, 1.107657674730919, 1.1076673938212553, 1.1076726642872978, 1.1076697849665404, 1.1077170071202043, 1.1077116424495026, 1.1076891927983237, 1.1076880123609256, 1.107680081034169, 1.1076894502083783, 1.1077092751285853, 1.1077081567815807, 1.1077017248347028, 1.107708156776961, 1.1077149123566457, 1.1077166533261693, 1.1077190594798063, 1.107704957578069, 1.10769282948806, 1.1076825201777527, 1.1076929479312931, 1.1076926137823124, 1.1076918047169888, 1.107691618669079, 1.1076877971468415, 1.107680104406204, 1.1076808276160608, 1.1076720619140656, 1.1075031480953914, 1.107409145856145, 1.1073109859453216, 1.1072406854886756, 1.107126434336597, 1.1070488180180873, 1.1069887444884556, 1.1069304428470548, 1.1068922179014413, 1.1068916337128683, 1.1068834080550727, 1.106902865018033, 1.1069539731268956, 1.107015768039785, 1.1069893954179162, 1.1069772647292202, 1.1069691759571418, 1.106969731542514, 1.1069845133824414, 1.1069992163906703, 1.1070130815911736, 1.1070378907787484, 1.1070650854033977, 1.1070760123666334, 1.1071312332495187, 1.1072345595063773, 1.1073311857639814, 1.107337019912643, 1.1073429415121958, 1.107344830121777, 1.1073450716353488, 1.1073508018174145, 1.1073510365288217, 1.1073499660671975, 1.107347640906899, 1.1073496191071244, 1.1073439414785053, 1.107351994671169, 1.1073527673011965, 1.1073472417883519, 1.1073521991298982, 1.1073630960564875, 1.10734404832002, 1.1073014204598306, 1.107272812957242, 1.107253296505147, 1.1072369585328639, 1.1072319166652738, 1.1072283045034725, 1.1072263833314007, 1.1072246510858201, 1.1072260641011746, 1.1072269433913053, 1.1072080687234496, 1.107206900771523, 1.1071978591325078, 1.1071777954558277, 1.1071600317096277, 1.1071475335594494, 1.107146116835751, 1.1071431613106073, 1.1071380822597363, 1.1071244295844147, 1.107126841756692, 1.1071272042099691, 1.107098668393312, 1.1070572600945436, 1.1069957862786115, 1.1069414868977823, 1.1068760357825893, 1.1068596749898423, 1.1068381452586724, 1.1068163137992932, 1.1068216568561435, 1.1068230954190446, 1.1068133992954683, 1.10679625229433, 1.106795991525175, 1.1067997134910488, 1.1067619907034996, 1.1067134793697693, 1.106513408559544, 1.1063866660350063, 1.106255914209617, 1.1061071380715806, 1.106006777570364, 1.1058848874720093, 1.105817532696972, 1.105687117324539, 1.1056122590221307]}, {"line": {"color": "green", "width": 1}, "name": "BBU", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349], "y": [1.107566587581347, 1.1075863948849165, 1.1075950417095908, 1.1076074110011955, 1.1076147422465032, 1.107599969236768, 1.1075957611303693, 1.1076065980216727, 1.1076009816432553, 1.1075888456971106, 1.1075705971667835, 1.107570173925777, 1.1075665680424052, 1.107560825148321, 1.1075627850373146, 1.107562785018155, 1.1075551132997525, 1.1075596058931507, 1.1075636897586, 1.1075833723849058, 1.1075791004393458, 1.1075809473166232, 1.1075791322433313, 1.1075896277578836, 1.1076369657419551, 1.1077051666195523, 1.1077091457640262, 1.107760686683999, 1.1079115783686768, 1.107996763857936, 1.1080665444318538, 1.1081392791607825, 1.1081966561138226, 1.1082114175884674, 1.108212010821255, 1.1081824876381685, 1.108145755682079, 1.1081315854163918, 1.1081282355999715, 1.1081612341916827, 1.108109337721402, 1.1081251615977685, 1.108118003219196, 1.1081198977967777, 1.1081136934767537, 1.1080948752425, 1.1081191000536423, 1.108126696219931, 1.1081446025563402, 1.1081963294202437, 1.108332820640534, 1.1083670995304087, 1.1083839212701416, 1.1082897232345361, 1.1082086319678315, 1.1080560811788536, 1.107973277986133, 1.1078840282381097, 1.1077680526101898, 1.1076118543420659, 1.1075290388319972, 1.1074121525109653, 1.1072992592926505, 1.1072333368031395, 1.1072427876924567, 1.1072430750750677, 1.10723961237143, 1.1072342554400347, 1.10721094637376, 1.1071871522104686, 1.1071646975743805, 1.1071673275578962, 1.1071670799163527, 1.1071654265906112, 1.1071153863664704, 1.1070265338353655, 1.1069577403336914, 1.10696262313708, 1.1070109795426883, 1.1070330560204606, 1.1070399796767654, 1.107039800125623, 1.107051324635135, 1.1070981992459654, 1.107122262994463, 1.1071207455365426, 1.1071244503765314, 1.107201047108463, 1.1073127990161822, 1.1073859068644385, 1.1073885288814518, 1.107403608488921, 1.1074775303547857, 1.1075060038245612, 1.1075123206620088, 1.1075178268682906, 1.10752582623544, 1.1075687456437375, 1.107690560211515, 1.1078127677845055, 1.1079040181224489, 1.1079694196312044, 1.108033994769924, 1.108058069203045, 1.1080611917227827, 1.1080430905783638, 1.108051289335543, 1.1080428283176313, 1.1079931788759732, 1.1079767348044733, 1.1079600481983838, 1.10794533602885, 1.1079369212491736, 1.107909163489389, 1.1078582491639628, 1.107911814101843, 1.1079655608068013, 1.1080510742212257, 1.1081058520833666, 1.108145251612026, 1.1081532512947019, 1.1081600442133677, 1.108159427461627, 1.108149406158427, 1.1081316405549648, 1.1082059565028304, 1.1082591861028814, 1.1082538813557479, 1.108250964252935, 1.108246311038621, 1.1082657613925313, 1.1082476803983163, 1.1082356245151972, 1.1082947469337259, 1.1083317754491224, 1.1083551590587795, 1.1083717963926902, 1.1083825337376825, 1.1083606221816482, 1.1082399097084519, 1.10810333571525, 1.1080806834389385, 1.1081010342580508, 1.10815437622026, 1.1082276776609046, 1.1082769018858223, 1.1083314224771172, 1.1083383096386703, 1.1083684576489885, 1.1083924443001536, 1.1083876843436395, 1.10836681687334, 1.108344053862466, 1.1083197827144227, 1.1082694736668302, 1.1082843429010136, 1.1082804322804822, 1.108274399355593, 1.1083169306685097, 1.1083106177689612, 1.1082896211556286, 1.1082913595436301, 1.1083123939747541, 1.1083197633087953, 1.1083548323889456, 1.1083849338834644, 1.1083941311807317, 1.1084051134224127, 1.1084185145977472, 1.1083964017829384, 1.1084055806512054, 1.1084055806512054, 1.1082968979926722, 1.1082519828194388, 1.1082359395302153, 1.108223797378366, 1.1082399810568764, 1.1082545678707636, 1.1082322141395442, 1.108209750520682, 1.1082126091679514, 1.1082430852704583, 1.108299496144992, 1.1083499817747036, 1.1083580072465953, 1.1083582323925631, 1.1083698654176404, 1.1084054512576769, 1.1083999247328102, 1.1083977406401004, 1.1084042926258995, 1.1084103311279987, 1.1084106287851114, 1.108404842723125, 1.108370173404272, 1.1083103843556161, 1.1082319400522758, 1.1081286306681941, 1.1080801165303993, 1.1080594062829692, 1.1080878993066736, 1.1080757492081694, 1.1080723728476656, 1.1080757492104298, 1.1080736867648449, 1.108082280815053, 1.1080869264314708, 1.1080857337335086, 1.1080858040224133, 1.108078426601622, 1.1080501522094413, 1.108025571254567, 1.1080002194152179, 1.1079677892400865, 1.1079041707955561, 1.1079030500672227, 1.1078880599257575, 1.1078875203473493, 1.1079122604511666, 1.1079106668558867, 1.1079106668558867, 1.107922206957773, 1.1080543384082222, 1.108069251454688, 1.108075600670582, 1.1080757612348426, 1.108078196893779, 1.108101310749618, 1.1081167500099172, 1.1081143384100238, 1.1081052872313948, 1.1081021992638396, 1.108102325269085, 1.108098320464463, 1.1080959071412775, 1.1080945007477492, 1.1079987071655142, 1.1079883575505014, 1.1079908072016802, 1.10799055906765, 1.1079827761086922, 1.1079376926487687, 1.1078978677285616, 1.1079004146469948, 1.107916846593873, 1.1079004146516145, 1.1079008019290726, 1.1079004895309776, 1.1078995119487693, 1.1078964709933634, 1.1078943133690868, 1.1078931941079657, 1.1078956234972823, 1.1079002433605487, 1.1079153381401576, 1.107941238473782, 1.1079593457103052, 1.1079784670223716, 1.1079691723839429, 1.107967938085938, 1.1080711376188979, 1.1081094255724304, 1.1081447283403965, 1.1081607430827565, 1.1081949942348355, 1.1082011819819164, 1.1081798269401193, 1.1081467000100917, 1.108089210669991, 1.1079983662871358, 1.1078951633735028, 1.1077499921248282, 1.1075988840159654, 1.1075099462459328, 1.1075663188678018, 1.1075970209850687, 1.1076336811857186, 1.1076388398860608, 1.1076769151889903, 1.107697926466476, 1.1077040612659728, 1.1077063949355408, 1.10771062888232, 1.1077125590619412, 1.1077059096076276, 1.1076611547793407, 1.1076002428074507, 1.1075858372302176, 1.107567058487808, 1.1075580270210836, 1.1075449283646548, 1.1075434838968747, 1.1075218206140387, 1.1075143196470916, 1.1075152162359614, 1.1075146666071642, 1.1075146299500689, 1.1075137196145484, 1.1075058041273778, 1.1074970439259366, 1.1074978008701042, 1.1074740468006579, 1.1074730945371252, 1.107485722397315, 1.1074914727570464, 1.1074909892091414, 1.10749161289571, 1.1074823690490143, 1.107478838353673, 1.1074850452400304, 1.1074924917713254, 1.107482507327399, 1.1074801994658396, 1.107480502705124, 1.1074830992284788, 1.1074707122960656, 1.1074750616870317, 1.1074828254332312, 1.1074867521548382, 1.1074867403071083, 1.107486838689395, 1.107484774883123, 1.1074827132727305, 1.1074574439575962, 1.1074270815043188, 1.1074199030352618, 1.1074055970483159, 1.1074284994356764, 1.107405655959363, 1.1074153927888417, 1.107404610724446, 1.1073975690270443, 1.1073851147721376, 1.1073669145724303, 1.1073369045809578, 1.1072994578473911, 1.1072708905628152, 1.107215437046256, 1.107163143651811, 1.10715086643936, 1.1071565206302334, 1.1072651628690298, 1.1073147625364248, 1.1073583715046713, 1.1073785762141362, 1.1073646510010673, 1.1073536839565645, 1.1072581815887452, 1.1072043112468921, 1.1071277409778721]}, {"marker": {"color": "MediumPurple", "size": 10}, "mode": "markers", "name": "Signal", "type": "scatter", "x": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349], "y": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1.10739, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1.10753, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "from datetime import datetime\n", "st=10400\n", "dfpl = df[st:st+350]\n", "dfpl.reset_index(inplace=True)\n", "fig = go.Figure(data=[go.Candlestick(x=dfpl.index,\n", "                open=dfpl['Open'],\n", "                high=dfpl['High'],\n", "                low=dfpl['Low'],\n", "                close=dfpl['Close']),\n", "                go.<PERSON>er(x=dfpl.index, y=dfpl.VWAP, \n", "                           line=dict(color='blue', width=1), \n", "                           name=\"VWAP\"), \n", "                go.<PERSON>(x=dfpl.index, y=dfpl['BBL_14_2.0'], \n", "                           line=dict(color='green', width=1), \n", "                           name=\"BBL\"),\n", "                go.<PERSON>er(x=dfpl.index, y=dfpl['BBU_14_2.0'], \n", "                           line=dict(color='green', width=1), \n", "                           name=\"BBU\")])\n", "\n", "fig.add_scatter(x=dfpl.index, y=dfpl['pointposbreak'], mode=\"markers\",\n", "                marker=dict(size=10, color=\"MediumPurple\"),\n", "                name=\"Signal\")\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 9, "id": "6083727a", "metadata": {}, "outputs": [], "source": ["dfpl = df[:75000].copy()\n", "import pandas_ta as ta\n", "dfpl['ATR']=ta.atr(dfpl.High, dfpl.Low, dfpl.Close, length=7)\n", "#help(ta.atr)\n", "def SIGNAL():\n", "    return dfpl.TotalSignal"]}, {"cell_type": "code", "execution_count": 10, "id": "81219ec4", "metadata": {}, "outputs": [{"data": {"text/plain": ["Start                     2019-09-30 00:00:00\n", "End                       2020-09-30 04:25:00\n", "Duration                    366 days 04:25:00\n", "Exposure Time [%]                    6.694667\n", "Equity Final [$]                   172.713956\n", "Equity Peak [$]                    173.665736\n", "Return [%]                          72.713956\n", "Buy & Hold Return [%]                7.246403\n", "Return (Ann.) [%]                   53.432392\n", "Volatility (Ann.) [%]               25.126177\n", "Sharpe <PERSON>io                         2.126563\n", "<PERSON><PERSON><PERSON>                        5.880902\n", "Calmar Ratio                         5.285867\n", "Max. Drawdown [%]                   -10.10854\n", "Avg. Drawdown [%]                   -0.707265\n", "Max. Drawdown Duration       53 days 00:40:00\n", "Avg. Drawdown Duration        1 days 19:34:00\n", "# Trades                                  713\n", "Win Rate [%]                        48.527349\n", "Best Trade [%]                        0.42012\n", "Worst Trade [%]                      -0.27659\n", "Avg. Trade [%]                       0.007964\n", "Max. Trade Duration           2 days 05:30:00\n", "Avg. Trade Duration           0 days 00:47:00\n", "Profit Factor                        1.346955\n", "Expectancy [%]                       0.007989\n", "SQN                                   2.84368\n", "_strategy                             MyStrat\n", "_equity_curve                             ...\n", "_trades                        Size  Entry...\n", "dtype: object"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from backtesting import Strategy\n", "from backtesting import Backtest\n", "\n", "class MyStrat(Strategy):\n", "    initsize = 0.99\n", "    mysize = initsize\n", "    def init(self):\n", "        super().init()\n", "        self.signal1 = self.I(SIGNAL)\n", "\n", "    def next(self):\n", "        super().next()\n", "        slatr = 1.2*self.data.ATR[-1]\n", "        TPSLRatio = 1.5\n", "\n", "        if len(self.trades)>0:\n", "            if self.trades[-1].is_long and self.data.RSI[-1]>=90:\n", "                self.trades[-1].close()\n", "            elif self.trades[-1].is_short and self.data.RSI[-1]<=10:\n", "                self.trades[-1].close()\n", "        \n", "        if self.signal1==2 and len(self.trades)==0:\n", "            sl1 = self.data.Close[-1] - slatr\n", "            tp1 = self.data.Close[-1] + slatr*TPSLRatio\n", "            self.buy(sl=sl1, tp=tp1, size=self.mysize)\n", "        \n", "        elif self.signal1==1 and len(self.trades)==0:         \n", "            sl1 = self.data.Close[-1] + slatr\n", "            tp1 = self.data.Close[-1] - slatr*TPSLRatio\n", "            self.sell(sl=sl1, tp=tp1, size=self.mysize)\n", "\n", "bt = Backtest(dfpl, MyStrat, cash=100, margin=1/10, commission=0.00)\n", "stat = bt.run()\n", "stat"]}, {"cell_type": "code", "execution_count": 11, "id": "16396f6c", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\apps\\Python-3.10.11\\lib\\site-packages\\backtesting\\_plotting.py:122: UserWarning:\n", "\n", "Data contains too many candlesticks to plot; downsampling to '1H'. See `Backtest.plot(resample=...)`\n", "\n"]}, {"ename": "ValueError", "evalue": "Length of values (2) does not match length of index (1)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32md:\\data\\learn\\CodeTrading\\36 - Python Backtest： Profitable Scalping Strategy with VWAP, Bollinger Bands and RSI Indicators\\VWAP_Scalping_01.ipynb Cell 11\u001b[0m line \u001b[0;36m1\n\u001b[1;32m----> <a href='vscode-notebook-cell:/d%3A/data/learn/CodeTrading/36%20-%20Python%20Backtest%EF%BC%9A%20Profitable%20Scalping%20Strategy%20with%20VWAP%2C%20Bollinger%20Bands%20and%20RSI%20Indicators/VWAP_Scalping_01.ipynb#X13sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m bt\u001b[39m.\u001b[39;49mplot(show_legend\u001b[39m=\u001b[39;49m\u001b[39mFalse\u001b[39;49;00m)\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\backtesting\\backtesting.py:1592\u001b[0m, in \u001b[0;36mBacktest.plot\u001b[1;34m(self, results, filename, plot_width, plot_equity, plot_return, plot_pl, plot_volume, plot_drawdown, smooth_equity, relative_equity, superimpose, resample, reverse_indicators, show_legend, open_browser)\u001b[0m\n\u001b[0;32m   1589\u001b[0m         \u001b[39mraise\u001b[39;00m \u001b[39mRuntimeError\u001b[39;00m(\u001b[39m'\u001b[39m\u001b[39mFirst issue `backtest.run()` to obtain results.\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m   1590\u001b[0m     results \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_results\n\u001b[1;32m-> 1592\u001b[0m \u001b[39mreturn\u001b[39;00m plot(\n\u001b[0;32m   1593\u001b[0m     results\u001b[39m=\u001b[39;49mresults,\n\u001b[0;32m   1594\u001b[0m     df\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_data,\n\u001b[0;32m   1595\u001b[0m     indicators\u001b[39m=\u001b[39;49mresults\u001b[39m.\u001b[39;49m_strategy\u001b[39m.\u001b[39;49m_indicators,\n\u001b[0;32m   1596\u001b[0m     filename\u001b[39m=\u001b[39;49mfilename,\n\u001b[0;32m   1597\u001b[0m     plot_width\u001b[39m=\u001b[39;49mplot_width,\n\u001b[0;32m   1598\u001b[0m     plot_equity\u001b[39m=\u001b[39;49mplot_equity,\n\u001b[0;32m   1599\u001b[0m     plot_return\u001b[39m=\u001b[39;49mplot_return,\n\u001b[0;32m   1600\u001b[0m     plot_pl\u001b[39m=\u001b[39;49mplot_pl,\n\u001b[0;32m   1601\u001b[0m     plot_volume\u001b[39m=\u001b[39;49mplot_volume,\n\u001b[0;32m   1602\u001b[0m     plot_drawdown\u001b[39m=\u001b[39;49mplot_drawdown,\n\u001b[0;32m   1603\u001b[0m     smooth_equity\u001b[39m=\u001b[39;49msmooth_equity,\n\u001b[0;32m   1604\u001b[0m     relative_equity\u001b[39m=\u001b[39;49mrelative_equity,\n\u001b[0;32m   1605\u001b[0m     superimpose\u001b[39m=\u001b[39;49msuperimpose,\n\u001b[0;32m   1606\u001b[0m     resample\u001b[39m=\u001b[39;49mresample,\n\u001b[0;32m   1607\u001b[0m     reverse_indicators\u001b[39m=\u001b[39;49mreverse_indicators,\n\u001b[0;32m   1608\u001b[0m     show_legend\u001b[39m=\u001b[39;49mshow_legend,\n\u001b[0;32m   1609\u001b[0m     open_browser\u001b[39m=\u001b[39;49mopen_browser)\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\backtesting\\_plotting.py:203\u001b[0m, in \u001b[0;36mplot\u001b[1;34m(results, df, indicators, filename, plot_width, plot_equity, plot_return, plot_pl, plot_volume, plot_drawdown, smooth_equity, relative_equity, superimpose, resample, reverse_indicators, show_legend, open_browser)\u001b[0m\n\u001b[0;32m    201\u001b[0m \u001b[39m# Limit data to max_candles\u001b[39;00m\n\u001b[0;32m    202\u001b[0m \u001b[39mif\u001b[39;00m is_datetime_index:\n\u001b[1;32m--> 203\u001b[0m     df, indicators, equity_data, trades \u001b[39m=\u001b[39m _maybe_resample_data(\n\u001b[0;32m    204\u001b[0m         resample, df, indicators, equity_data, trades)\n\u001b[0;32m    206\u001b[0m df\u001b[39m.\u001b[39mindex\u001b[39m.\u001b[39mname \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m  \u001b[39m# Provides source name @index\u001b[39;00m\n\u001b[0;32m    207\u001b[0m df[\u001b[39m'\u001b[39m\u001b[39mdatetime\u001b[39m\u001b[39m'\u001b[39m] \u001b[39m=\u001b[39m df\u001b[39m.\u001b[39mindex  \u001b[39m# Save original, maybe datetime index\u001b[39;00m\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\backtesting\\_plotting.py:153\u001b[0m, in \u001b[0;36m_maybe_resample_data\u001b[1;34m(resample_rule, df, indicators, equity_data, trades)\u001b[0m\n\u001b[0;32m    150\u001b[0m     \u001b[39mreturn\u001b[39;00m f\n\u001b[0;32m    152\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mlen\u001b[39m(trades):  \u001b[39m# Avoid pandas \"resampling on Int64 index\" error\u001b[39;00m\n\u001b[1;32m--> 153\u001b[0m     trades \u001b[39m=\u001b[39m trades\u001b[39m.\u001b[39;49massign(count\u001b[39m=\u001b[39;49m\u001b[39m1\u001b[39;49m)\u001b[39m.\u001b[39;49mresample(freq, on\u001b[39m=\u001b[39;49m\u001b[39m'\u001b[39;49m\u001b[39mExitTime\u001b[39;49m\u001b[39m'\u001b[39;49m, label\u001b[39m=\u001b[39;49m\u001b[39m'\u001b[39;49m\u001b[39mright\u001b[39;49m\u001b[39m'\u001b[39;49m)\u001b[39m.\u001b[39;49magg(\u001b[39mdict\u001b[39;49m(\n\u001b[0;32m    154\u001b[0m         TRADES_AGG,\n\u001b[0;32m    155\u001b[0m         ReturnPct\u001b[39m=\u001b[39;49m_weighted_returns,\n\u001b[0;32m    156\u001b[0m         count\u001b[39m=\u001b[39;49m\u001b[39m'\u001b[39;49m\u001b[39msum\u001b[39;49m\u001b[39m'\u001b[39;49m,\n\u001b[0;32m    157\u001b[0m         EntryBar\u001b[39m=\u001b[39;49m_group_trades(\u001b[39m'\u001b[39;49m\u001b[39mEntryTime\u001b[39;49m\u001b[39m'\u001b[39;49m),\n\u001b[0;32m    158\u001b[0m         ExitBar\u001b[39m=\u001b[39;49m_group_trades(\u001b[39m'\u001b[39;49m\u001b[39mExitTime\u001b[39;49m\u001b[39m'\u001b[39;49m),\n\u001b[0;32m    159\u001b[0m     ))\u001b[39m.\u001b[39mdropna()\n\u001b[0;32m    161\u001b[0m \u001b[39mreturn\u001b[39;00m df, indicators, equity_data, trades\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\resample.py:352\u001b[0m, in \u001b[0;36mResampler.aggregate\u001b[1;34m(self, func, *args, **kwargs)\u001b[0m\n\u001b[0;32m    343\u001b[0m \u001b[39m@doc\u001b[39m(\n\u001b[0;32m    344\u001b[0m     _shared_docs[\u001b[39m\"\u001b[39m\u001b[39maggregate\u001b[39m\u001b[39m\"\u001b[39m],\n\u001b[0;32m    345\u001b[0m     see_also\u001b[39m=\u001b[39m_agg_see_also_doc,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    349\u001b[0m )\n\u001b[0;32m    350\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39maggregate\u001b[39m(\u001b[39mself\u001b[39m, func\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, \u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs):\n\u001b[1;32m--> 352\u001b[0m     result \u001b[39m=\u001b[39m ResamplerWindowApply(\u001b[39mself\u001b[39;49m, func, args\u001b[39m=\u001b[39;49margs, kwargs\u001b[39m=\u001b[39;49mkwargs)\u001b[39m.\u001b[39;49magg()\n\u001b[0;32m    353\u001b[0m     \u001b[39mif\u001b[39;00m result \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[0;32m    354\u001b[0m         how \u001b[39m=\u001b[39m func\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\apply.py:172\u001b[0m, in \u001b[0;36mApply.agg\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    169\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mapply_str()\n\u001b[0;32m    171\u001b[0m \u001b[39mif\u001b[39;00m is_dict_like(arg):\n\u001b[1;32m--> 172\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49magg_dict_like()\n\u001b[0;32m    173\u001b[0m \u001b[39melif\u001b[39;00m is_list_like(arg):\n\u001b[0;32m    174\u001b[0m     \u001b[39m# we require a list, but not a 'str'\u001b[39;00m\n\u001b[0;32m    175\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39magg_list_like()\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\apply.py:504\u001b[0m, in \u001b[0;36mApply.agg_dict_like\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    501\u001b[0m     results \u001b[39m=\u001b[39m {key: colg\u001b[39m.\u001b[39magg(how) \u001b[39mfor\u001b[39;00m key, how \u001b[39min\u001b[39;00m arg\u001b[39m.\u001b[39mitems()}\n\u001b[0;32m    502\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m    503\u001b[0m     \u001b[39m# key used for column selection and output\u001b[39;00m\n\u001b[1;32m--> 504\u001b[0m     results \u001b[39m=\u001b[39m {\n\u001b[0;32m    505\u001b[0m         key: obj\u001b[39m.\u001b[39m_gotitem(key, ndim\u001b[39m=\u001b[39m\u001b[39m1\u001b[39m)\u001b[39m.\u001b[39magg(how) \u001b[39mfor\u001b[39;00m key, how \u001b[39min\u001b[39;00m arg\u001b[39m.\u001b[39mitems()\n\u001b[0;32m    506\u001b[0m     }\n\u001b[0;32m    508\u001b[0m \u001b[39m# set the final keys\u001b[39;00m\n\u001b[0;32m    509\u001b[0m keys \u001b[39m=\u001b[39m \u001b[39mlist\u001b[39m(arg\u001b[39m.\u001b[39mkeys())\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\apply.py:505\u001b[0m, in \u001b[0;36m<dictcomp>\u001b[1;34m(.0)\u001b[0m\n\u001b[0;32m    501\u001b[0m     results \u001b[39m=\u001b[39m {key: colg\u001b[39m.\u001b[39magg(how) \u001b[39mfor\u001b[39;00m key, how \u001b[39min\u001b[39;00m arg\u001b[39m.\u001b[39mitems()}\n\u001b[0;32m    502\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[0;32m    503\u001b[0m     \u001b[39m# key used for column selection and output\u001b[39;00m\n\u001b[0;32m    504\u001b[0m     results \u001b[39m=\u001b[39m {\n\u001b[1;32m--> 505\u001b[0m         key: obj\u001b[39m.\u001b[39;49m_gotitem(key, ndim\u001b[39m=\u001b[39;49m\u001b[39m1\u001b[39;49m)\u001b[39m.\u001b[39;49magg(how) \u001b[39mfor\u001b[39;00m key, how \u001b[39min\u001b[39;00m arg\u001b[39m.\u001b[39mitems()\n\u001b[0;32m    506\u001b[0m     }\n\u001b[0;32m    508\u001b[0m \u001b[39m# set the final keys\u001b[39;00m\n\u001b[0;32m    509\u001b[0m keys \u001b[39m=\u001b[39m \u001b[39mlist\u001b[39m(arg\u001b[39m.\u001b[39mkeys())\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\groupby\\generic.py:297\u001b[0m, in \u001b[0;36mSeriesGroupBy.aggregate\u001b[1;34m(self, func, engine, engine_kwargs, *args, **kwargs)\u001b[0m\n\u001b[0;32m    294\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_python_agg_general(func, \u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n\u001b[0;32m    296\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m--> 297\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_python_agg_general(func, \u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n\u001b[0;32m    298\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mKeyError\u001b[39;00m:\n\u001b[0;32m    299\u001b[0m     \u001b[39m# TODO: Key<PERSON><PERSON><PERSON> is raised in _python_agg_general,\u001b[39;00m\n\u001b[0;32m    300\u001b[0m     \u001b[39m#  see test_groupby.test_basic\u001b[39;00m\n\u001b[0;32m    301\u001b[0m     result \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_aggregate_named(func, \u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py:1682\u001b[0m, in \u001b[0;36mGroupBy._python_agg_general\u001b[1;34m(self, func, raise_on_typeerror, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1678\u001b[0m name \u001b[39m=\u001b[39m obj\u001b[39m.\u001b[39mname\n\u001b[0;32m   1680\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m   1681\u001b[0m     \u001b[39m# if this function is invalid for this dtype, we will ignore it.\u001b[39;00m\n\u001b[1;32m-> 1682\u001b[0m     result \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mgrouper\u001b[39m.\u001b[39;49magg_series(obj, f)\n\u001b[0;32m   1683\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mTypeError\u001b[39;00m:\n\u001b[0;32m   1684\u001b[0m     \u001b[39mif\u001b[39;00m raise_on_typeerror:\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\groupby\\ops.py:1081\u001b[0m, in \u001b[0;36mBaseGrouper.agg_series\u001b[1;34m(self, obj, func, preserve_dtype)\u001b[0m\n\u001b[0;32m   1078\u001b[0m     preserve_dtype \u001b[39m=\u001b[39m \u001b[39mTrue\u001b[39;00m\n\u001b[0;32m   1080\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m-> 1081\u001b[0m     result \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_aggregate_series_pure_python(obj, func)\n\u001b[0;32m   1083\u001b[0m npvalues \u001b[39m=\u001b[39m lib\u001b[39m.\u001b[39mmaybe_convert_objects(result, try_float\u001b[39m=\u001b[39m\u001b[39mFalse\u001b[39;00m)\n\u001b[0;32m   1084\u001b[0m \u001b[39mif\u001b[39;00m preserve_dtype:\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\groupby\\ops.py:1104\u001b[0m, in \u001b[0;36mBaseGrouper._aggregate_series_pure_python\u001b[1;34m(self, obj, func)\u001b[0m\n\u001b[0;32m   1101\u001b[0m splitter \u001b[39m=\u001b[39m get_splitter(obj, ids, ngroups, axis\u001b[39m=\u001b[39m\u001b[39m0\u001b[39m)\n\u001b[0;32m   1103\u001b[0m \u001b[39mfor\u001b[39;00m i, group \u001b[39min\u001b[39;00m \u001b[39menumerate\u001b[39m(splitter):\n\u001b[1;32m-> 1104\u001b[0m     res \u001b[39m=\u001b[39m func(group)\n\u001b[0;32m   1105\u001b[0m     res \u001b[39m=\u001b[39m libreduction\u001b[39m.\u001b[39mextract_result(res)\n\u001b[0;32m   1107\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m initialized:\n\u001b[0;32m   1108\u001b[0m         \u001b[39m# We only do this validation on the first iteration\u001b[39;00m\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py:1668\u001b[0m, in \u001b[0;36mGroupBy._python_agg_general.<locals>.<lambda>\u001b[1;34m(x)\u001b[0m\n\u001b[0;32m   1665\u001b[0m \u001b[39m@final\u001b[39m\n\u001b[0;32m   1666\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_python_agg_general\u001b[39m(\u001b[39mself\u001b[39m, func, \u001b[39m*\u001b[39margs, raise_on_typeerror\u001b[39m=\u001b[39m\u001b[39mFalse\u001b[39;00m, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs):\n\u001b[0;32m   1667\u001b[0m     func \u001b[39m=\u001b[39m com\u001b[39m.\u001b[39mis_builtin_func(func)\n\u001b[1;32m-> 1668\u001b[0m     f \u001b[39m=\u001b[39m \u001b[39mlambda\u001b[39;00m x: func(x, \u001b[39m*\u001b[39margs, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs)\n\u001b[0;32m   1670\u001b[0m     \u001b[39m# iterate through \"columns\" ex exclusions to populate output dict\u001b[39;00m\n\u001b[0;32m   1671\u001b[0m     output: \u001b[39mdict\u001b[39m[base\u001b[39m.\u001b[39mOutput<PERSON>ey, <PERSON><PERSON><PERSON><PERSON>ike] \u001b[39m=\u001b[39m {}\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\backtesting\\_plotting.py:147\u001b[0m, in \u001b[0;36m_maybe_resample_data.<locals>._group_trades.<locals>.f\u001b[1;34m(s, new_index, bars)\u001b[0m\n\u001b[0;32m    144\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mf\u001b[39m(s, new_index\u001b[39m=\u001b[39mpd\u001b[39m.\u001b[39mIndex(df\u001b[39m.\u001b[39mindex\u001b[39m.\u001b[39mview(\u001b[39mint\u001b[39m)), bars\u001b[39m=\u001b[39mtrades[column]):\n\u001b[0;32m    145\u001b[0m     \u001b[39mif\u001b[39;00m s\u001b[39m.\u001b[39msize:\n\u001b[0;32m    146\u001b[0m         \u001b[39m# Via int64 because on pandas recently broken datetime\u001b[39;00m\n\u001b[1;32m--> 147\u001b[0m         mean_time \u001b[39m=\u001b[39m \u001b[39mint\u001b[39m(bars\u001b[39m.\u001b[39;49mloc[s\u001b[39m.\u001b[39;49mindex]\u001b[39m.\u001b[39;49mview(\u001b[39mint\u001b[39;49m)\u001b[39m.\u001b[39mmean())\n\u001b[0;32m    148\u001b[0m         new_bar_idx \u001b[39m=\u001b[39m new_index\u001b[39m.\u001b[39mget_loc(mean_time, method\u001b[39m=\u001b[39m\u001b[39m'\u001b[39m\u001b[39mnearest\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m    149\u001b[0m         \u001b[39mreturn\u001b[39;00m new_bar_idx\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\series.py:839\u001b[0m, in \u001b[0;36mSeries.view\u001b[1;34m(self, dtype)\u001b[0m\n\u001b[0;32m    836\u001b[0m \u001b[39m# self.array instead of self._values so we piggyback on PandasArray\u001b[39;00m\n\u001b[0;32m    837\u001b[0m \u001b[39m#  implementation\u001b[39;00m\n\u001b[0;32m    838\u001b[0m res_values \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39marray\u001b[39m.\u001b[39mview(dtype)\n\u001b[1;32m--> 839\u001b[0m res_ser \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_constructor(res_values, index\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mindex)\n\u001b[0;32m    840\u001b[0m \u001b[39mreturn\u001b[39;00m res_ser\u001b[39m.\u001b[39m__finalize__(\u001b[39mself\u001b[39m, method\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mview\u001b[39m\u001b[39m\"\u001b[39m)\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\series.py:461\u001b[0m, in \u001b[0;36mSeries.__init__\u001b[1;34m(self, data, index, dtype, name, copy, fastpath)\u001b[0m\n\u001b[0;32m    459\u001b[0m     index \u001b[39m=\u001b[39m default_index(\u001b[39mlen\u001b[39m(data))\n\u001b[0;32m    460\u001b[0m \u001b[39melif\u001b[39;00m is_list_like(data):\n\u001b[1;32m--> 461\u001b[0m     com\u001b[39m.\u001b[39;49mrequire_length_match(data, index)\n\u001b[0;32m    463\u001b[0m \u001b[39m# create/copy the manager\u001b[39;00m\n\u001b[0;32m    464\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39misinstance\u001b[39m(data, (SingleBlockManager, SingleArrayManager)):\n", "File \u001b[1;32md:\\apps\\Python-3.10.11\\lib\\site-packages\\pandas\\core\\common.py:571\u001b[0m, in \u001b[0;36mrequire_length_match\u001b[1;34m(data, index)\u001b[0m\n\u001b[0;32m    567\u001b[0m \u001b[39m\u001b[39m\u001b[39m\"\"\"\u001b[39;00m\n\u001b[0;32m    568\u001b[0m \u001b[39mCheck the length of data matches the length of the index.\u001b[39;00m\n\u001b[0;32m    569\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[0;32m    570\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mlen\u001b[39m(data) \u001b[39m!=\u001b[39m \u001b[39mlen\u001b[39m(index):\n\u001b[1;32m--> 571\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[0;32m    572\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mLength of values \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    573\u001b[0m         \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m(\u001b[39m\u001b[39m{\u001b[39;00m\u001b[39mlen\u001b[39m(data)\u001b[39m}\u001b[39;00m\u001b[39m) \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    574\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mdoes not match length of index \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    575\u001b[0m         \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m(\u001b[39m\u001b[39m{\u001b[39;00m\u001b[39mlen\u001b[39m(index)\u001b[39m}\u001b[39;00m\u001b[39m)\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m    576\u001b[0m     )\n", "\u001b[1;31mValueError\u001b[0m: Length of values (2) does not match length of index (1)"]}], "source": ["bt.plot(show_legend=False)"]}, {"cell_type": "code", "execution_count": null, "id": "1efd22a2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}