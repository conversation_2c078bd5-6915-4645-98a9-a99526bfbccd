<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace>QuantConnect.Algorithm</RootNamespace>
    <AssemblyName>QuantConnect.Algorithm</AssemblyName>
    <TargetFramework>net9.0</TargetFramework>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AnalysisMode>AllEnabledByDefault</AnalysisMode>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <DocumentationFile>bin\$(Configuration)\QuantConnect.Algorithm.xml</DocumentationFile>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Description>QuantConnect LEAN Engine: Algorithm Project - Core QCAlgorithm implementation</Description>
    <NoWarn>CA1062</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup>
    <PackageLicenseFile>LICENSE</PackageLicenseFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="QuantConnect.pythonnet" Version="2.0.48" />
    <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="NodaTime" Version="3.0.5" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\Common\Properties\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Configuration\QuantConnect.Configuration.csproj" />
    <ProjectReference Include="..\Indicators\QuantConnect.Indicators.csproj" />
    <ProjectReference Include="..\Common\QuantConnect.csproj" />
    <ProjectReference Include="..\Logging\QuantConnect.Logging.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Alphas\NullAlphaModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Execution\ImmediateExecutionModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Execution\NullExecutionModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Portfolio\NullPortfolioConstructionModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Risk\CompositeRiskManagementModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Risk\NullRiskManagementModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Selection\ManualUniverseSelectionModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Selection\UniverseSelectionModel.py">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\LICENSE">
      <Pack>True</Pack>
      <PackagePath></PackagePath>
    </None>
  </ItemGroup>
</Project>
