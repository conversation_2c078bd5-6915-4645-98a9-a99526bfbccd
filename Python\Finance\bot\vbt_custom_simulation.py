# fmt: off
import os
import sys
import inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
import utils
import json
import schwab_villa
# fmt: on

import datetime as dt
import logging
import math
import subprocess
import sys
import random
import timeit
import humanfriendly
import threading
from pyinstrument import Profiler
import time
from concurrent.futures import thread
import pandas as pd
import pandas_ta as ta
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
import warnings
import pandas as pd
import numba
import strategies
import vectorbtpro as vbt
from collections import namedtuple
import pandas as pd
import pendulum
from aligo import Aligo
from numba import njit
from typing import NamedTuple, Callable, Any, Optional, Dict, Tuple


btc_granularity = 1e-5  # BTC min. granularity on OKX and Binance is 0.00001
spot_fees = 0.00 / 100
future_fees = 0.05 / 100
min_order_value = 1  # minimum single order value(quantity * price) for DOGEFDUSD is 1, for BTCFDUSD is 5

initsize = 0.618  # max is 1.0
assert initsize <= 1.0
init_cash = 100
my_size_granularity = 1
my_leverage = 10
my_fees = 0.00 / 100
my_fixed_fees = 0.0  # not tested, potential bugs
laddering_diff = 1e-5
required_gap = laddering_diff * 3
os.environ["timeframe"] = "1"  # default: 5min, might be changed during runtime

# Probability of rejecting this order to simulate a random rejection event.
# Not everything goes smoothly in real life. Use random rejections to test your order management for robustness.
my_reject_prob = 0.0
assert 0 <= my_reject_prob <= 1.0

assert my_size_granularity <= 1.0
assert my_leverage <= 50
assert 1 > my_fees >= 0
assert my_fixed_fees >= 0
assert my_fees == 0 or my_fixed_fees == 0

DO_PARAMETERIZATION = 1
exit_ladder_info_dt = np.dtype(
    [
        ("price", np.float64),
        ("amount", np.float64),
        ("hit", np.float64),
    ]
)
entry_ladder_info_dt = np.dtype(
    [
        ("bar_inx", np.int64),
        ("price", np.float64),
        ("asset_value_pct", np.float64),
        ("cost", np.float64),
        ("position_changed", np.float64),
    ]
)


@numba.njit(nogil=True)
def operate_order(
    bar_inx,
    price,
    price_area,
    quantity,
    exec_state,
    order_records,
    order_counts,
    trading_volume,
):
    """
    This function should contain only the order logic.
    """
    my_size_type = vbt.pf_enums.SizeType.Amount
    my_direction = vbt.pf_enums.Direction.Both
    size_granularity = my_size_granularity

    value = exec_state.cash + price * exec_state.position
    if value <= 0:
        return vbt.pf_enums.OrderResult(0.0, 0.0, 0.0, 0, 0, 0), exec_state, False
    exec_state = vbt.pf_enums.ExecState(
        cash=exec_state.cash,
        position=exec_state.position,
        debt=exec_state.debt,
        locked_cash=exec_state.locked_cash,
        free_cash=exec_state.free_cash,
        val_price=price,
        value=value,
    )
    order = vbt.pf_nb.order_nb(
        size=quantity,
        price=exec_state.val_price,
        size_type=my_size_type,
        direction=my_direction,
        fees=my_fees,
        fixed_fees=my_fixed_fees,
        size_granularity=size_granularity,
        leverage=my_leverage,
        leverage_mode=vbt.pf_enums.LeverageMode.Eager,
        reject_prob=my_reject_prob,
        price_area_vio_mode=vbt.pf_enums.PriceAreaVioMode.Error,
        allow_partial=False,
    )
    if 0:
        vbt.pprint(order)
        vbt.pprint(price_area)
        vbt.pprint(exec_state)
    order_result, exec_state = vbt.pf_nb.process_order_nb(
        0,
        0,
        bar_inx,
        exec_state=exec_state,
        order=order,
        # price_area=price_area, it's possible previous close not in price_area
        order_records=order_records,
        order_counts=order_counts,
    )

    if exec_state.position != 0:
        assert abs(exec_state.position) >= my_size_granularity

    ret = order_result.status == 0 and order_result.status_info == -1
    if ret:
        assert abs(order_result.size * order_result.price) > min_order_value
    trading_volume[0] += abs(order_result.size * order_result.price)

    return order_result, exec_state, ret


@numba.njit(nogil=True)
def precheck_laddering(start_price, end_price, n_levels):
    """Calculates the price of the very first ladder level."""
    if n_levels <= 0:
        return False

    if abs(start_price - end_price) < 1e-9 and n_levels > 1:
        return False

    diff_unit = (end_price - start_price) / n_levels
    first_level_price = start_price + 1 * diff_unit

    assert laddering_diff > 0
    exponent = int(-math.log10(laddering_diff))
    exponent = max(0, exponent)
    first_level_price = round(first_level_price, exponent)

    return abs(first_level_price - start_price) >= required_gap


@numba.njit(nogil=True)
def populate_laddering(position, start_price, end_price, n_levels, laddering, sl=True):
    assert position != 0
    assert not np.isinf(end_price) and not np.isinf(start_price)
    assert start_price > 0 and end_price > 0
    assert abs(start_price - end_price) / n_levels > laddering_diff  # Prevent too-close prices

    granularity_cnt = abs(position) / my_size_granularity
    assert granularity_cnt >= 1

    if granularity_cnt > n_levels:
        pict_laddering = utils.get_interval_lists(n_levels)
    else:
        n_levels = abs(int(position))
        pict_laddering = utils.get_interval_lists(n_levels)

    laddering = np.zeros((n_levels,), dtype=exit_ladder_info_dt)
    diff_unit = (end_price - start_price) / n_levels
    assert abs(diff_unit) > laddering_diff

    total_assigned = 0.0  # Track the total assigned position
    precision = int(-np.log10(my_size_granularity)) if my_size_granularity < 1 else 0
    sign = -1 if position > 0 else 1

    for inx in range(n_levels):
        ladder_inx_price = start_price + (inx + 1) * diff_unit
        exponent = int(-math.log10(laddering_diff))
        laddering[inx]["price"] = round(ladder_inx_price, exponent)

        if 0 and inx == 0:
            if abs(laddering[inx]["price"] - start_price) < 10 * laddering_diff:
                logging.info(f'Smaller: {1e+5 * abs(laddering[inx]["price"] - start_price)}')
            else:
                logging.info(f'Ideal: {1e+5 * abs(laddering[inx]["price"] - start_price)}')

        if inx < n_levels - 1:
            order_size = abs(round(pict_laddering[inx] / 100 * position, precision))
            total_assigned += order_size
        else:
            # Ensure last entry fills the remaining position exactly
            order_size = abs(round(position - total_assigned, precision))

        laddering[inx]["amount"] = order_size * sign
        laddering[inx]["hit"] = 0.0

        assert abs(laddering[inx]["price"] * laddering[inx]["amount"]) > min_order_value

    return True, laddering


@numba.njit(nogil=True)
def ohlv_check(
    exec_state,
    bar_inx,
    price_area,
    laddering,
    is_stop_laddering,
    order_records,
    order_counts,
    trading_volume,
):
    long_stop_loss = False
    long_take_profit = False
    short_stop_loss = False
    short_take_profit = False

    assert exec_state.position != 0

    for loop_inx, xitem in enumerate(laddering):
        if xitem["hit"] != 0.0:
            continue

        if xitem["price"] == 0.0:
            break

        val_price = xitem["price"]
        adj_price = val_price > price_area.high or val_price < price_area.low
        if exec_state.position == 0:
            break
        elif exec_state.position > 0:
            long_stop_loss = is_stop_laddering and price_area.low <= val_price  # trigger long stop-loss
            long_take_profit = not is_stop_laddering and price_area.high >= val_price  # trigger long take-profit
        elif exec_state.position < 0:
            short_stop_loss = is_stop_laddering and price_area.high >= val_price  # trigger short stop-loss
            short_take_profit = not is_stop_laddering and price_area.low <= val_price  # trigger short take-profit

        condition_met = np.any(np.array([long_stop_loss, long_take_profit, short_stop_loss, short_take_profit]))
        if not condition_met:
            break

        if adj_price:  # price jump
            val_price = price_area.open

        if loop_inx == (len(laddering) - 1):
            order_size = -exec_state.position
        else:
            order_size = xitem["amount"]

        if exec_state.position == 0:
            break

        if loop_inx < len(laddering) - 1:  # not last item
            # Calculate how much position will remain after executing the current order
            remaining_position = abs(exec_state.position) - abs(order_size)

            # Calculate total unfilled amount from remaining ladder orders
            total_unfilled_amount = 0.0
            for i in range(loop_inx + 1, len(laddering)):
                if laddering[i]["hit"] == 0.0 and laddering[i]["amount"] != 0.0:
                    total_unfilled_amount += abs(laddering[i]["amount"])

            # If remaining position is smaller than total unfilled amount, adjust the ladder
            if remaining_position < total_unfilled_amount:
                # Adjust remaining ladder orders to match remaining position
                remaining_to_close = remaining_position
                for i in range(loop_inx + 1, len(laddering)):
                    if laddering[i]["hit"] == 0.0 and remaining_to_close > 0:
                        order_size_for_level = min(abs(laddering[i]["amount"]), remaining_to_close)
                        
                        # Check if order meets minimum order value requirement
                        if order_size_for_level * laddering[i]["price"] < min_order_value:
                            # If this is the last level or would be too small, merge with current order
                            if i == len(laddering) - 1 or remaining_to_close <= order_size_for_level:
                                # Close remaining position with current order
                                order_size = -remaining_position if exec_state.position > 0 else remaining_position
                                # Mark remaining ladder as hit to prevent further execution
                                for j in range(loop_inx + 1, len(laddering)):
                                    laddering[j]["hit"] = 1.0
                                break
                        else:
                            # Adjust ladder order size
                            laddering[i]["amount"] = -order_size_for_level if exec_state.position > 0 else order_size_for_level
                            remaining_to_close -= order_size_for_level
                    else:
                        # Mark as hit if no remaining position
                        laddering[i]["hit"] = 1.0

        order_result, exec_state, ok = operate_order(
            bar_inx,
            val_price,
            price_area,
            order_size,
            exec_state,
            order_records,
            order_counts,
            trading_volume,
        )
        if not ok:
            return exec_state, False
        laddering[loop_inx]["hit"] = 1.0

    return exec_state, True


@numba.njit(nogil=True)
def pipeline_nb(
    atr_tp_ratio,
    atr_sl_ratio,
    n_entries,
    num_step_entries,
    sl_levels,
    tp_levels,
    data_context,
    signals,
):
    # To ensure prompt stop-loss execution, cautious stop-profit management, and to allow profits to accumulate,
    # it is imperative that the number of stop-loss levels (sl_levels) be less than the number of take-profit levels (tp_levels).
    assert sl_levels < tp_levels

    close_2d = vbt.nb.to_2d_array_nb(data_context.Close)
    target_shape = close_2d.shape
    trading_volume = np.zeros(1, dtype=np.float_)
    returns = np.zeros((target_shape[0], 1), dtype=np.float_)

    signals_cnt = np.count_nonzero(signals)
    long_signals_cnt = np.count_nonzero(signals == 2)
    short_signals_cnt = np.count_nonzero(signals == 1)
    total_signal_pct = signals_cnt / target_shape[0]
    
    # Early return if signal density is too low (< 15%)
    if total_signal_pct < 0.01:
        benchmark_return = (data_context.Close[-1] - data_context.Close[0]) / data_context.Close[0]
        empty_order_records = np.empty((0,), dtype=vbt.pf_enums.order_dt)  # 1D array to match flattened output
        return (
            0.0,  # max_dd_days
            0.0,  # max_dd_bars
            0.0,  # max_drawdown
            0.0,  # total_return
            0.0,  # win_rate
            np.int32(0),  # order_counts - use np.int32 to match order_counts[0] type
            benchmark_return,  # benchmark_return
            0.0,  # sharpe_ratio
            long_signals_cnt,  # long_signals_cnt
            short_signals_cnt,  # short_signals_cnt
            total_signal_pct,  # total_signal_pct
            0.0,  # trading_volume
            empty_order_records,  # order_records
        )
    
    max_possible_orders = signals_cnt * 2 * (tp_levels + sl_levels)
    order_records = np.empty((max_possible_orders, 1), dtype=vbt.pf_enums.order_dt)
    order_counts = np.full(1, 0, dtype=np.int_)  # only one group
    entry_laddering = np.zeros((n_entries,), dtype=entry_ladder_info_dt)
    tp_laddering = np.zeros((tp_levels,), dtype=exit_ladder_info_dt)
    sl_laddering = np.zeros((sl_levels,), dtype=exit_ladder_info_dt)

    exec_state = vbt.pf_enums.ExecState(
        cash=float(init_cash),
        position=0.0,
        debt=0.0,
        locked_cash=0.0,
        free_cash=float(init_cash),
        val_price=np.nan,
        value=np.nan,
    )

    last_value = exec_state.cash

    long_stop_loss = np.inf
    long_take_profit = np.inf

    short_stop_loss = np.inf
    short_take_profit = np.inf

    opening_position = 0
    value_before_opening_position = np.inf

    _, atr_value = vbt.indicators.nb.atr_1d_nb(
        high=data_context.High,
        low=data_context.Low,
        close=data_context.Close,
        window=7,
        wtype=vbt.generic.enums.WType.Wilder,
    )
    for bar_inx in range(data_context.Close.shape[0]):
        if bar_inx < 20:  # for the sake of simulation testing
            continue
        cur_open = data_context.Open[bar_inx]
        cur_close = data_context.Close[bar_inx]
        cur_high = data_context.High[bar_inx]
        cur_low = data_context.Low[bar_inx]
        cur_signal = signals[bar_inx]

        mysize = initsize

        price_area = vbt.pf_enums.PriceArea(open=cur_open, high=cur_high, low=cur_low, close=cur_close)
        while True:
            if not opening_position:
                break

            entry_info_inx_ary = np.where(entry_laddering["bar_inx"] == bar_inx)[0]
            if len(entry_info_inx_ary) <= 0:
                break

            entry_info_inx = entry_info_inx_ary[0]
            if 0 == entry_info_inx:
                assert exec_state.position == 0.0
                value_before_opening_position = mysize * exec_state.cash

            val_price = cur_open

            entry_laddering[entry_info_inx]["price"] = val_price
            asset_value_pct = entry_laddering[entry_info_inx]["asset_value_pct"]
            open_trading_inx = entry_laddering[0]["bar_inx"]
            assert bar_inx >= open_trading_inx
            average_atr = np.mean(atr_value[open_trading_inx - 1 : bar_inx])
            average_close = np.mean(data_context.Close[open_trading_inx - 1 : bar_inx])
            init_position = exec_state.position

            next_potential_order_cash = abs(mysize * exec_state.free_cash * my_leverage * asset_value_pct)
            next_potential_order_size = math.floor(next_potential_order_cash / (val_price * (1 + my_fees)))
            if next_potential_order_size <= 0:
                opening_position = 0
                break

            entry_laddering[entry_info_inx]["cost"] = val_price * next_potential_order_size
            accumulated_cost = np.sum(entry_laddering["cost"])
            entry_laddering[entry_info_inx]["position_changed"] = next_potential_order_size
            accumulated_position = np.sum(entry_laddering["position_changed"])
            entry_price = abs(accumulated_cost / accumulated_position)

            evil_signal = False
            # fix stop-loss price to avoid liquidation
            max_price_diff = abs(value_before_opening_position / accumulated_position)
            if opening_position == 2:  # long
                long_stop_loss = average_close - atr_sl_ratio * average_atr
                long_stop_loss = np.maximum(entry_price - max_price_diff, long_stop_loss)
                long_take_profit = average_close + atr_tp_ratio * average_atr

                # fmt: off
                if (long_stop_loss < 0 or 
                    (entry_price <= long_stop_loss) or 
                    abs(entry_price - long_stop_loss) / sl_levels <= laddering_diff or 
                    abs(entry_price - long_take_profit) / tp_levels <= laddering_diff):
                    evil_signal = True
            elif opening_position == 1:  # short
                short_stop_loss = average_close + atr_sl_ratio * average_atr
                short_stop_loss = np.minimum(entry_price + max_price_diff, short_stop_loss)
                short_take_profit = average_close - atr_tp_ratio * average_atr

                # fmt: off
                if (short_take_profit < 0 or 
                    (short_stop_loss <= entry_price) or 
                    abs(entry_price - short_stop_loss) / sl_levels <= laddering_diff or 
                    abs(entry_price - short_take_profit) / tp_levels <= laddering_diff):
                    evil_signal = True

            if evil_signal:
                opening_position = 0
                break

            stop_loss = long_stop_loss if not np.isinf(long_stop_loss) else short_stop_loss
            take_profit = long_take_profit if not np.isinf(long_take_profit) else short_take_profit
            if not precheck_laddering(entry_price, stop_loss, sl_levels) or not precheck_laddering(entry_price, take_profit, tp_levels):
                opening_position = 0
                break
            order_result, exec_state, ok = operate_order(
                bar_inx,
                val_price,
                price_area,
                abs(next_potential_order_size) * (-1 if asset_value_pct < 0 else 1),
                exec_state,
                order_records,
                order_counts,
                trading_volume,
            )
            if not ok:
                opening_position = 0
                break
            assert exec_state.position != 0
            position_changed = abs(exec_state.position - init_position)
            assert abs(next_potential_order_size - position_changed) < 1e-5

            ok, sl_laddering = populate_laddering(
                exec_state.position,
                entry_price,
                stop_loss,
                sl_levels,
                sl_laddering,
                True,
            )
            if not ok:
                opening_position = 0
                break
            ok, tp_laddering = populate_laddering(
                exec_state.position,
                entry_price,
                take_profit,
                tp_levels,
                tp_laddering,
                False,
            )
            if not ok:
                opening_position = 0
                break

            index_of_last_item = len(entry_laddering) - 1
            if entry_info_inx == index_of_last_item:
                opening_position = 0

            break  # exit while-loop, only trade-enter order is filled at one candle

        # check stop-loss
        if exec_state.position != 0:
            exec_state, ok = ohlv_check(
                exec_state,
                bar_inx,
                price_area,
                sl_laddering,
                True,
                order_records,
                order_counts,
                trading_volume,
            )
            if not ok:
                break
            for loop_inx, xitem in enumerate(sl_laddering):
                if xitem["hit"] == 1.0:
                    opening_position = 0
                    break

        # check take-profit
        if exec_state.position != 0:
            exec_state, ok = ohlv_check(
                exec_state,
                bar_inx,
                price_area,
                tp_laddering,
                False,
                order_records,
                order_counts,
                trading_volume,
            )
            if not ok:
                break
            for inx in range(len(tp_laddering) - 1, -1, -1):
                if tp_laddering[inx]["hit"] != 0.0:
                    opening_position = 0

                    sl_laddering = np.zeros((1,), dtype=exit_ladder_info_dt)
                    sl_laddering[0]["price"] = tp_laddering[inx]["price"]
                    sl_laddering[0]["amount"] = 1.0
                    sl_laddering[0]["hit"] = 0.0
                    break

        if exec_state.position == 0:  # position is closed by profit-loss
            opening_position = 0

        cur_value = exec_state.cash + cur_close * exec_state.position
        returns[bar_inx] = (cur_value - last_value) / last_value
        last_value = cur_value

        if exec_state.position != 0:
            continue

        # stop trading at 40% loss
        if (exec_state.cash / init_cash) <= 0.6:
            break

        tp_laddering = np.zeros((tp_levels,), dtype=exit_ladder_info_dt)
        sl_laddering = np.zeros((sl_levels,), dtype=exit_ladder_info_dt)
        long_take_profit = np.inf
        short_take_profit = np.inf
        long_stop_loss = np.inf
        short_stop_loss = np.inf
        entry_price = np.inf
        value_before_opening_position = np.inf

        if cur_signal != 2 and cur_signal != 1:
            continue

        long = cur_signal == 2
        opening_position = cur_signal
        entry_laddering = np.zeros((n_entries,), dtype=entry_ladder_info_dt)
        next_bar_inx = bar_inx + 1

        free_cash_pct = 1.0 if long else -1.0
        for entry_signal_inx in range(n_entries):
            entry_laddering[entry_signal_inx]["bar_inx"] = next_bar_inx + num_step_entries * entry_signal_inx
            entry_laddering[entry_signal_inx]["asset_value_pct"] = free_cash_pct / (n_entries - entry_signal_inx)

    order_records = order_records[: order_counts[0]].flatten()

    col_map = vbt.rec_nb.col_map_nb(col_arr=order_records["col"], n_cols=target_shape[1])
    total_return = vbt.ret_nb.total_return_nb(returns)

    trade_records = vbt.pf_nb.get_exit_trades_nb(order_records, close_2d, col_map)
    # trade_records = trade_records[trade_records["status"] == vbt.pf_enums.TradeStatus.Closed]
    win_rate = vbt.pf_nb.win_rate_reduce_nb(trade_records["pnl"])

    max_drawdown = vbt.ret_nb.max_drawdown_nb(returns)
    # Annualization factor is the average number of bars in a year, such as 365
    ann_factor = 60 / data_context.freq_in_minutes * 24 * 365
    sharpe_ratio = vbt.ret_nb.sharpe_ratio_nb(returns, ann_factor=ann_factor)

    cum_returns = vbt.ret_nb.cumulative_returns_nb(returns)
    drawdown_records = vbt.nb.get_drawdowns_nb(None, None, None, cum_returns)
    dd_duration = vbt.nb.range_duration_nb(drawdown_records["start_idx"], drawdown_records["end_idx"], drawdown_records["status"])
    dd_col_map = vbt.rec_nb.col_map_nb(drawdown_records["col"], returns.shape[1])
    max_dd_bars = vbt.rec_nb.reduce_mapped_nb(dd_duration, dd_col_map, np.nan, vbt.nb.max_reduce_nb)
    max_dd_days = vbt.utils.datetime_nb.days_nb(max_dd_bars * vbt.utils.datetime_nb.m_ns * data_context.freq_in_minutes)

    benchmark_return = (data_context.Close[-1] - data_context.Close[0]) / data_context.Close[0]

    if DO_PARAMETERIZATION != 0:
        order_records = np.empty((0,), dtype=vbt.pf_enums.order_dt)  # to save memory when do parameterization - 1D array to match flattened output
    return (
        max_dd_days[0],
        max_dd_bars[0],
        max_drawdown[0],
        total_return[0],
        win_rate,
        order_counts[0],
        benchmark_return,
        sharpe_ratio[0],
        long_signals_cnt,
        short_signals_cnt,
        total_signal_pct,
        trading_volume[0],
        order_records,
    )


@vbt.parameterized(
    merge_func=(
        "concat",  # max_dd_days
        "concat",  # max_dd_bars
        "concat",  # max_drawdown
        "concat",  # total_return
        "concat",  # win_rate
        "concat",  # order_cnt
        "concat",  # benchmark_return
        "concat",  # sharpe_ratio
        "concat",  # long_signals_cnt
        "concat",  # short_signals_cnt
        "concat",  # total_signal_pct
        "concat",  # trading_volume
        None,  # order_records - don't merge this
    ),
    merge_kwargs=dict(keys=vbt.Rep("param_index")),
    execute_kwargs=dict(
        show_progress=True,
        chunk_len="auto",
        engine="threadpool",
        warmup=True,
    ),
)
def trading_pipeline(
    data_context,
    trading_params,
    signal_func,
    **signal_params,
):
    # Unpack signal parameters in order
    signal_args = list(signal_params.values())

    # Generate signals using the provided signal_func
    signals = signal_func(data_context, *signal_args)

    # Use the generated signals in pipeline_nb
    return pipeline_nb(
        data_context=data_context,
        signals=signals,
        **trading_params,
    )


def unit_test():
    global DO_PARAMETERIZATION
    DO_PARAMETERIZATION = 0
    data_context = strategies.get_data(as_namedtuple=True)

    trading_params = {
        "atr_sl_ratio": 1.6,
        "atr_tp_ratio": 1.6,
        "n_entries": 1,
        "num_step_entries": 2,
        "sl_levels": 3,
        "tp_levels": 12,
    }

    # hard limit of Binance
    # { MAX_NUM_ORDERS: 200, MAX_NUM_ALGO_ORDERS: 5}
    assert trading_params["sl_levels"] <= 5

    signal_params = {
        "rsi_len": 6,
        "ema_slow_window": 42,
        "ema_fast_window": 24,
        "bb_window": 13,
        "bb_std": 1.1,
        "backcandles": 7,
    }
    assert trading_params["atr_tp_ratio"] >= trading_params["atr_sl_ratio"]
    assert trading_params["tp_levels"] > trading_params["sl_levels"]  # Prompt stop-loss and delayed profit-taking
    with vbt.Timer() as timer:

        (
            max_dd_days,
            max_dd_bars,
            max_drawdown,
            total_return,
            win_rate,
            order_cnt,
            benchmark_return,
            sharpe_ratio,
            long_signals_cnt,
            short_signals_cnt,
            total_signal_pct,
            trading_volume,
            order_records,
        ) = trading_pipeline(
            data_context=data_context,
            trading_params=trading_params,
            signal_func=strategies.get_ema_bbands_signals_nb,
            **signal_params,
        )
    logging.info(f"first call cost: `{timer.elapsed()}`")

    broadcasted_args, wrapper = vbt.broadcast(data_context.Close, return_wrapper=True)
    timeframe = int(os.environ["timeframe"])
    pf = vbt.Portfolio(
        wrapper.replace(freq=f"{timeframe}m"),
        order_records,
        open=data_context.Open,
        high=data_context.High,
        low=data_context.Low,
        close=data_context.Close,
        init_cash=init_cash,
    )

    """
    winning_trade_returns = pf.trades.winning.get_projections(pf.log_returns, rebase=False)
    losing_trade_returns = pf.trades.losing.get_projections(pf.log_returns, rebase=False)
    avg_winning_trade_return = vbt.pd_acc.returns(winning_trade_returns, log_returns=True).total().mean()
    avg_losing_trade_return = vbt.pd_acc.returns(losing_trade_returns, log_returns=True).total().mean()
    """
    if 0:
        logging.info(f"pf.orders.records_readable:\n{pf.orders.records_readable}")
        trades = pf.trades.records_readable
        trades["PnL"] = trades["PnL"].apply(lambda x: f"{x:.2f}" if not pd.isna(x) else "N/A")
        logging.info(f"pf.trades.records_readable:\n{trades}")

        total_bars = data_context.Close.shape[0]
        pf.iloc[0:total_bars].plot().auto_rangebreaks().show()

        value = pf.value.apply(lambda x: format(x, ".2f"))
        logging.info(f"\n{value}")
        for xvalue in value:
            if float(xvalue) < 0:
                assert False, "Not all items are greater than 0"

        logging.info(f"-> pf.stats: \n{pf.stats()}")

    vbt_max_drawdown = pf.deep_getattr("max_drawdown")
    assert math.isclose(vbt_max_drawdown, max_drawdown, abs_tol=1e-6)

    vbt_benchmark_return = pf.deep_getattr("bm_returns.vbt.returns.total")
    assert math.isclose(vbt_benchmark_return, benchmark_return)
    benchmark_return *= 100
    benchmark_return = round(benchmark_return, 2)

    vbt_max_dd_days = pf.deep_getattr("drawdowns.max_duration")
    vbt_max_dd_days = 0.0 if np.isnan(vbt_max_dd_days.days) else vbt_max_dd_days.days
    max_dd_days = 0.0 if np.isnan(max_dd_days) else int(max_dd_days)
    max_dd_bars = 0.0 if np.isnan(max_dd_bars) else int(max_dd_bars)
    assert max_dd_days == vbt_max_dd_days
    max_drawdown *= 100
    max_drawdown = round(max_drawdown, 2)

    vbt_total_return = pf.deep_getattr("total_return")
    assert math.isclose(total_return, vbt_total_return, rel_tol=1e-5)
    total_return *= 100
    total_return = round(total_return, 2)

    vbt_win_rate = pf.deep_getattr("trades.win_rate")
    vbt_win_rate = 0 if np.isnan(vbt_win_rate) else vbt_win_rate
    win_rate = 0 if np.isnan(win_rate) else win_rate
    assert math.isclose(win_rate, vbt_win_rate)
    win_rate *= 100
    win_rate = round(win_rate, 2)

    # fmt: off
    logging.info(f"\n\tConfig:\n"
                 f"\t          init_cash: {init_cash}\n"
                 f"\t        my_leverage: {my_leverage}\n"
                 f"\t    min_order_value: {min_order_value}\n"
                 f"\t     laddering_diff: {laddering_diff}\n"
                 f"\t            my_fees: {my_fees}\n"
                 f"\t      my_fixed_fees: {my_fixed_fees}\n"
                 f"\t     my_reject_prob: {my_reject_prob}\n"
                 f"\tmy_size_granularity: {my_size_granularity}\n"
    )

    logging.info(f"\nSignalParams:\n"
                 f"{json.dumps(signal_params, indent=4)}\n"
    )
    logging.info(f"\nTradingParams:\n"
                 f"{json.dumps(trading_params, indent=4)}\n"
    )

    logging.info(f"\nStatistics:\n"
                 f"\t     max_dd_days: {max_dd_days}\n"
                 f"\t     max_dd_bars: {int(max_dd_bars)}\n"
                 f"\t    max_drawdown: {max_drawdown}%\n"
                 f"\t    total_return: {total_return}%\n"
                 f"\tbenchmark_return: {benchmark_return}%\n"
                 f"\t    sharpe_ratio: {sharpe_ratio:.2f}%\n"
                 f"\t        win_rate: {win_rate}%\n"
                 f"\t       order_cnt: {order_cnt}\n"
                 f"\t long_signals_cnt: {long_signals_cnt}\n"
                 f"\tshort_signals_cnt: {short_signals_cnt}\n"
                 f"\t total_signal_pct: {total_signal_pct:.2%}\n"
                 f"\t  trading_volume: {humanfriendly.format_number(int(trading_volume))}\n"
    )
    # fmt: on


def handle():
    unit_test()


def main():
    if sys.platform == "win32":
        os.system("cls")
    else:
        os.system("clear")

    t0 = pendulum.now()

    utils.init_logging()

    handle()

    logging.info("-" * 60)
    t1 = pendulum.now()
    delta = t1 - t0
    logging.info(f"elapsed time: `{delta.in_words(locale='en')}`")


if __name__ == "__main__":
    if 1:
        main()
    else:
        with Profiler(interval=0.01) as profiler:
            main()
        profiler.open_in_browser()
