import datetime as dt
import logging
import math
import os
import subprocess
import sys
import threading
import time
from concurrent.futures import thread
import pandas as pd
import pandas_ta as ta
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
from backtesting import Strategy
from backtesting import Backtest
import inspect
import vbt_custom_simulation
import pandas as pd
import pendulum
import strategies

currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0, parentdir)
import utils
from aligo import Aligo


def handle():
    df, not_used_context = strategies.vwap_bbands()

    init_cash = 100
    TPSLRatio = 1.5
    SLATRRatio = 1.2
    my_leverage = 10

    def SIGNAL():
        return df.TotalSignal

    class MyStrat(Strategy):
        initsize = 0.99
        mysize = initsize

        def init(self):
            super().init()
            self.signal1 = self.I(SIGNAL)

        def next(self):
            super().next()
            slatr = SLATRRatio * self.data.ATR[-1]

            if len(self.trades) > 0:
                if self.trades[-1].is_long and self.data.RSI[-1] >= 90:
                    self.trades[-1].close()
                elif self.trades[-1].is_short and self.data.RSI[-1] <= 10:
                    self.trades[-1].close()

            if self.signal1 == 2 and len(self.trades) == 0:
                sl1 = self.data.Close[-1] - slatr
                tp1 = self.data.Close[-1] + slatr * TPSLRatio
                self.buy(sl=sl1, tp=tp1, size=self.mysize)

            elif self.signal1 == 1 and len(self.trades) == 0:
                sl1 = self.data.Close[-1] + slatr
                tp1 = self.data.Close[-1] - slatr * TPSLRatio
                self.sell(sl=sl1, tp=tp1, size=self.mysize)

    tic = time.time()
    bt = Backtest(df, MyStrat, cash=init_cash, margin=1 / my_leverage, commission=0.00)
    stat = bt.run()
    toc = time.time()
    logging.info(f"backtesting cost: {toc - tic:.2f}s")
    logging.info(f"\n{stat}")
    # bt.plot(show_legend=False)
    logging.info(f"stat._trades\n{stat._trades}")


def main():
    # clear the terminal
    if sys.platform == "win32":
        os.system("cls")
    else:
        os.system("reset")

    t0 = pendulum.now()

    utils.init_logging()

    handle()

    logging.info("-" * 69)
    t1 = pendulum.now()
    delta = t1 - t0
    logging.info(f"elapsed time: `{delta.in_words(locale='en')}`")

    print("about to exit~")


if __name__ == "__main__":
    main()
