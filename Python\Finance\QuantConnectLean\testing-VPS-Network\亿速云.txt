(base) root@yisu-67fc595ae040f:~# ab -n 1000 -c 10 "https://api.binance.com/api/v3/ticker/price?symbol=BNBBTC"
This is ApacheBench, Version 2.3 <$Revision: 1879490 $>
Copyright 1996 Adam <PERSON>, Zeus Technology Ltd, http://www.zeustech.net/
Licensed to The Apache Software Foundation, http://www.apache.org/

Benchmarking api.binance.com (be patient)
Completed 100 requests
Completed 200 requests
Completed 300 requests
Completed 400 requests
Completed 500 requests
Completed 600 requests
Completed 700 requests
Completed 800 requests
Completed 900 requests
Completed 1000 requests
Finished 1000 requests


Server Software:        nginx
Server Hostname:        api.binance.com
Server Port:            443
SSL/TLS Protocol:       TLSv1.2,ECDHE-RSA-AES128-GCM-SHA256,2048,128
Server Temp Key:        X25519 253 bits
TLS Server Name:        api.binance.com

Document Path:          /api/v3/ticker/price?symbol=BNBBTC
Document Length:        40 bytes

Concurrency Level:      10
Time taken for tests:   3.198 seconds
Complete requests:      1000
Failed requests:        0
Total transferred:      943896 bytes
HTML transferred:       40000 bytes
Requests per second:    312.71 [#/sec] (mean)
Time per request:       31.979 [ms] (mean)
Time per request:       3.198 [ms] (mean, across all concurrent requests)
Transfer rate:          288.24 [Kbytes/sec] received

Connection Times (ms)
              min  mean[+/-sd] median   max
Connect:        7   21   7.1     20     147
Processing:     4   11   4.5     10      74
Waiting:        4   10   4.2      9      74
Total:         15   31   9.6     30     221

Percentage of the requests served within a certain time (ms)
  50%     30
  66%     34
  75%     36
  80%     38
  90%     42
  95%     45
  98%     48
  99%     50
 100%    221 (longest request)
